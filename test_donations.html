<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التبرعات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2d5016;
            margin-top: 0;
        }
        button {
            background: #2d5016;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1a3009;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام التبرعات - موقع دعم غزة</h1>
        
        <div class="test-section">
            <h3>1. اختبار جلب التبرعات (GET)</h3>
            <button onclick="testGetDonations()">جلب جميع التبرعات</button>
            <button onclick="testGetDonationsByProject(1)">جلب تبرعات المشروع 1</button>
            <div id="getDonationsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار إضافة تبرع (POST)</h3>
            <button onclick="testAddDonation('EGP')">إضافة تبرع بالجنيه المصري</button>
            <button onclick="testAddDonation('USD')">إضافة تبرع بالدولار</button>
            <button onclick="testAddDonation('EUR')">إضافة تبرع باليورو</button>
            <div id="addDonationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار تحديث حالة التبرع (PUT)</h3>
            <button onclick="testUpdateDonationStatus('approved')">قبول التبرع</button>
            <button onclick="testUpdateDonationStatus('rejected')">رفض التبرع</button>
            <div id="updateDonationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار حذف تبرع (DELETE)</h3>
            <button onclick="testDeleteDonation()">حذف آخر تبرع</button>
            <div id="deleteDonationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار تحويل العملات</h3>
            <button onclick="testCurrencyConversion()">اختبار التحويل</button>
            <div id="currencyResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost/gaza/api/donations.php';
        let lastDonationId = null;

        // اختبار جلب التبرعات
        async function testGetDonations() {
            const resultDiv = document.getElementById('getDonationsResult');
            try {
                const response = await fetch(API_URL);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم جلب ${data.length} تبرع بنجاح:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطأ: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار جلب تبرعات مشروع محدد
        async function testGetDonationsByProject(projectId) {
            const resultDiv = document.getElementById('getDonationsResult');
            try {
                const response = await fetch(`${API_URL}?project_id=${projectId}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم جلب ${data.length} تبرع للمشروع ${projectId}:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطأ: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار إضافة تبرع
        async function testAddDonation(currency) {
            const resultDiv = document.getElementById('addDonationResult');
            
            const amounts = { 'EGP': 500, 'USD': 25, 'EUR': 20 };
            const countries = { 'EGP': 'مصر', 'USD': 'السعودية', 'EUR': 'الإمارات' };
            
            const donationData = {
                project_id: 1,
                donor_name: `متبرع تجريبي ${currency}`,
                donor_email: `test_${currency.toLowerCase()}@example.com`,
                donor_phone: '+201234567890',
                donor_country: countries[currency],
                amount: amounts[currency],
                currency: currency,
                message: `تبرع تجريبي بعملة ${currency}`,
                is_anonymous: false,
                status: 'pending'
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(donationData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    lastDonationId = data.donation_id;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم إضافة التبرع بنجاح!\nمعرف التبرع: ${data.donation_id}\nالمبلغ: ${amounts[currency]} ${currency}\nالمبلغ بالجنيه المصري: ${data.amount_in_egp} EGP`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطأ: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار تحديث حالة التبرع
        async function testUpdateDonationStatus(status) {
            const resultDiv = document.getElementById('updateDonationResult');
            
            if (!lastDonationId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ يجب إضافة تبرع أولاً لاختبار التحديث';
                return;
            }

            const updateData = {
                id: lastDonationId,
                status: status
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم تحديث حالة التبرع ${lastDonationId} إلى: ${status}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطأ: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار حذف تبرع
        async function testDeleteDonation() {
            const resultDiv = document.getElementById('deleteDonationResult');
            
            if (!lastDonationId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ يجب إضافة تبرع أولاً لاختبار الحذف';
                return;
            }

            try {
                const response = await fetch(`${API_URL}?id=${lastDonationId}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ تم حذف التبرع ${lastDonationId} بنجاح`;
                    lastDonationId = null;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ خطأ: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }

        // اختبار تحويل العملات
        function testCurrencyConversion() {
            const resultDiv = document.getElementById('currencyResult');
            
            const rates = { 'EGP': 1, 'USD': 49.5, 'EUR': 53.2 };
            const testAmounts = [
                { amount: 100, currency: 'USD' },
                { amount: 50, currency: 'EUR' },
                { amount: 1000, currency: 'EGP' }
            ];

            let results = '💱 اختبار تحويل العملات:\n\n';
            
            testAmounts.forEach(test => {
                const convertedAmount = test.amount * rates[test.currency];
                results += `${test.amount} ${test.currency} = ${convertedAmount} EGP\n`;
            });

            resultDiv.className = 'result info';
            resultDiv.textContent = results;
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            console.log('🚀 صفحة اختبار نظام التبرعات جاهزة');
            testCurrencyConversion();
        };
    </script>
</body>
</html>
