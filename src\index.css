
@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL and Arabic Typography Support */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 136 47% 35%;
    --primary-foreground: 210 40% 98%;
    --secondary: 43 100% 85%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 43 100% 70%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 136 47% 35%;
    --radius: 0.75rem;
    
    /* Custom Gaza colors */
    --gaza-green: 136 47% 35%;
    --gaza-red: 0 73% 41%;
    --gaza-gold: 43 100% 70%;
    --gaza-white: 0 0% 100%;
  }

  * {
    @apply border-border;
  }

  html {
    direction: rtl;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Amiri', 'Cairo', serif;
    font-weight: 700;
  }
}

@layer components {
  .hero-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--gaza-green)) 0%, 
      hsl(var(--gaza-gold)) 50%, 
      hsl(var(--gaza-green)) 100%);
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 px-8 py-3 rounded-lg font-semibold transition-all duration-300;
  }
}

@layer utilities {
  .rtl {
    direction: rtl;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
