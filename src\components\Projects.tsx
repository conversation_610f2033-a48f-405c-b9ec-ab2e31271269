
import React, { useState, useEffect } from 'react';
import { Users, Target, Calendar, ArrowLeft } from 'lucide-react';
import DonationModal from './DonationModal';

interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  target_amount: number;
  raised_amount: number;
  status: string;
  image_url?: string;
  created_at: string;
  percentage: number;
}

const Projects = () => {
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState('الكل');

  // جلب المشاريع من قاعدة البيانات
  const fetchProjects = async (page = 1, category = '') => {
    try {
      setLoading(true);

      // بناء معاملات URL
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '6', // عرض 6 مشاريع في الصفحة
        status: 'نشط' // عرض المشاريع النشطة فقط
      });

      if (category && category !== 'الكل') {
        params.append('category', category);
      }

      const response = await fetch(`http://localhost/gaza/api/projects.php?${params}`);
      const data = await response.json();

      if (data.success) {
        setProjects(data.data);
        setCurrentPage(data.pagination.current_page);
        setTotalPages(data.pagination.total_pages);
        setTotalRecords(data.pagination.total_records);
      } else {
        console.error('Error fetching projects:', data.message);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects(1, selectedCategory);
  }, [selectedCategory]);

  // فئات المشاريع
  const categories = ['الكل', 'طبي', 'غذائي', 'تعليمي', 'إسكان', 'مياه', 'ملابس', 'كهرباء', 'أخرى'];

  const handleDonateClick = (project: any) => {
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  const getDefaultImage = (category: string) => {
    const images: { [key: string]: string } = {
      'طبي': 'https://images.unsplash.com/photo-1584515933487-779824d29309?w=800&h=600&fit=crop',
      'غذائي': 'https://images.unsplash.com/photo-1593113598332-cd288d649433?w=800&h=600&fit=crop',
      'تعليمي': 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&h=600&fit=crop',
      'إسكان': 'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=800&h=600&fit=crop',
      'مياه': 'https://images.unsplash.com/photo-1541199249251-f713e6145474?w=800&h=600&fit=crop',
      'ملابس': 'https://images.unsplash.com/photo-1516627145497-ae4ef73a69e0?w=800&h=600&fit=crop'
    };
    return images[category] || 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&h=600&fit=crop';
  };

  if (loading) {
    return (
      <section id="projects" className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gaza-green mx-auto mb-4"></div>
            <p className="text-gray-600 font-amiri">جاري تحميل المشاريع...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="projects" className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gaza-green font-amiri">
            مشاريعنا الحالية
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            اختر المشروع الذي تريد دعمه وكن جزءاً من التغيير الإيجابي في حياة الآلاف من الأسر في غزة
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-3 rounded-full transition-all duration-300 font-semibold ${
                selectedCategory === category
                  ? 'bg-gaza-green text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-gaza-green hover:text-white shadow-md hover:shadow-lg hover:transform hover:scale-105'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Results Info */}
        {totalRecords > 0 && (
          <div className="text-center mb-8 text-gray-600 font-amiri">
            عرض {((currentPage - 1) * 6) + 1} إلى {Math.min(currentPage * 6, totalRecords)} من {totalRecords} مشروع
            {selectedCategory !== 'الكل' && ` في فئة "${selectedCategory}"`}
          </div>
        )}

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600 font-amiri text-lg">لا توجد مشاريع نشطة حالياً</p>
            </div>
          ) : (
            projects.map((project, index) => (
              <div
                key={project.id}
                className="bg-white rounded-2xl shadow-lg overflow-hidden card-hover animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Project Image */}
                <div className="relative overflow-hidden h-48">
                  <img
                    src={project.image_url ? `http://localhost/gaza/${project.image_url}` : getDefaultImage(project.category)}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    onError={(e) => {
                      // إذا فشل تحميل الصورة المرفوعة، استخدم الصورة الافتراضية
                      e.currentTarget.src = getDefaultImage(project.category);
                    }}
                  />
                  {project.status === 'عاجل' && (
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      عاجل
                    </div>
                  )}
                  <div className="absolute top-4 left-4 bg-gaza-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {project.category}
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-gaza-green font-amiri">
                    {project.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-semibold text-gaza-green">
                        {Number(project.raised_amount).toLocaleString()} ج.م
                      </span>
                      <span className="text-sm text-muted-foreground">
                        من {Number(project.target_amount).toLocaleString()} ج.م
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-gaza-green to-gaza-gold h-3 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min(project.percentage || 0, 100)}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {Math.round(project.percentage || 0)}% مكتمل
                    </div>
                  </div>

                  {/* Project Stats */}
                  <div className="flex items-center justify-between mb-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>متبرعون</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(project.created_at).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </div>

                  {/* Donate Button */}
                  <button
                    onClick={() => handleDonateClick(project)}
                    className="w-full btn-primary flex items-center justify-center gap-2 group"
                  >
                    تبرع الآن
                    <ArrowLeft className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center gap-4 mt-12">
            <button
              onClick={() => fetchProjects(currentPage - 1, selectedCategory)}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              السابق
            </button>

            <div className="flex items-center gap-2">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum: number;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => fetchProjects(pageNum, selectedCategory)}
                    className={`px-3 py-2 rounded-lg ${
                      currentPage === pageNum
                        ? 'bg-gaza-green text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => fetchProjects(currentPage + 1, selectedCategory)}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              التالي
            </button>
          </div>
        )}

        {/* Results Info */}
        <div className="text-center mt-4 text-gray-600">
          عرض {((currentPage - 1) * 6) + 1} إلى {Math.min(currentPage * 6, totalRecords)} من {totalRecords} مشروع
        </div>
      </div>

      {/* Donation Modal */}
      <DonationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        project={selectedProject}
      />
    </section>
  );
};

export default Projects;
