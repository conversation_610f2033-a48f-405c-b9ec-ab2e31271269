
import React, { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: "أحمد المصري",
      country: "مصر",
      rating: 5,
      text: "تجربة رائعة في التبرع، الموقع سهل الاستخدام والشفافية واضحة في كل شيء. أشعر بثقة كاملة أن تبرعي وصل لمستحقيه.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "فاطمة الزهراء",
      country: "المغرب",
      rating: 5,
      text: "ما يميز هذه المنظمة هو المتابعة المستمرة والتقارير الدورية. أتلقى تحديثات منتظمة عن المشاريع التي دعمتها.",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c9a95eec?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "محمد السعودي",
      country: "السعودية",
      rating: 5,
      text: "لقد ساهمت في عدة مشاريع والحمد لله رأيت النتائج بنفسي من خلال التقارير والصور. عمل احترافي ومؤثر حقاً.",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "ليلى القطري",
      country: "قطر",
      rating: 5,
      text: "منظمة موثوقة وشفافة، أنصح كل من يريد التبرع أن يختار هذه المنصة. الفريق محترف والتعامل راقي جداً.",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "عبدالله الإماراتي",
      country: "الإمارات",
      rating: 5,
      text: "سهولة في التبرع وسرعة في التنفيذ. المشاريع واضحة والأهداف محددة. أشعر أن تبرعي له تأثير حقيقي.",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "نور الكويتية",
      country: "الكويت",
      rating: 5,
      text: "تبرعت عدة مرات وفي كل مرة أتلقى تأكيد وتحديث عن استخدام التبرع. هذا ما يجعلني أثق وأستمر في الدعم.",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-5 w-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <section className="py-20 bg-gradient-to-r from-gaza-green to-gaza-gold">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white font-amiri">
            آراء المتبرعين
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            شهادات حقيقية من متبرعينا الكرام الذين وثقوا بنا وشاركونا في رحلة العطاء والتأثير الإيجابي
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-4xl mx-auto mb-12">
          <div className="bg-white rounded-2xl p-8 md:p-12 shadow-2xl animate-fade-in">
            <div className="flex flex-col items-center text-center">
              {/* Quote Icon */}
              <Quote className="h-12 w-12 text-gaza-gold mb-6" />
              
              {/* Rating */}
              <div className="flex items-center gap-1 mb-6">
                {renderStars(testimonials[currentTestimonial].rating)}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-lg md:text-xl text-foreground mb-8 leading-relaxed max-w-3xl">
                "{testimonials[currentTestimonial].text}"
              </blockquote>

              {/* Author Info */}
              <div className="flex items-center gap-4">
                <img
                  src={testimonials[currentTestimonial].avatar}
                  alt={testimonials[currentTestimonial].name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="text-right">
                  <div className="font-bold text-gaza-green text-lg font-amiri">
                    {testimonials[currentTestimonial].name}
                  </div>
                  <div className="text-muted-foreground">
                    {testimonials[currentTestimonial].country}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gaza-gold hover:text-white p-3 rounded-full shadow-lg transition-all duration-300 group"
          >
            <ChevronRight className="h-6 w-6 text-gaza-green group-hover:text-white" />
          </button>
          <button
            onClick={nextTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gaza-gold hover:text-white p-3 rounded-full shadow-lg transition-all duration-300 group"
          >
            <ChevronLeft className="h-6 w-6 text-gaza-green group-hover:text-white" />
          </button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center gap-2 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentTestimonial ? 'bg-white' : 'bg-white/40'
              }`}
            />
          ))}
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.slice(0, 6).map((testimonial, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-white animate-fade-in card-hover"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="font-semibold font-amiri">{testimonial.name}</div>
                  <div className="text-sm text-white/80">{testimonial.country}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-1 mb-3">
                {renderStars(testimonial.rating)}
              </div>
              
              <p className="text-sm text-white/90 leading-relaxed line-clamp-3">
                "{testimonial.text}"
              </p>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-bold text-white mb-4 font-amiri">
            انضم إلى أكثر من 25,000 متبرع حول العالم
          </h3>
          <button className="bg-white text-gaza-green hover:bg-gaza-gold hover:text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
            ابدأ رحلة التبرع معنا
          </button>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
