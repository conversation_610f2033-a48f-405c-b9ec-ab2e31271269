
import React, { useState, useEffect } from 'react';
import { ArrowLeft, Users, Target, Award } from 'lucide-react';
import DonationModal from './DonationModal';

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const heroSlides = [
    {
      title: "معاً لدعم أهل غزة",
      subtitle: "كن جزءاً من الحل واصنع الفرق في حياة الآلاف",
      description: "انضم إلينا في مساعدة الأسر المحتاجة في غزة من خلال تبرعاتك الكريمة"
    },
    {
      title: "مساعدات عاجلة لغزة",
      subtitle: "كل تبرع يصنع أملاً جديداً",
      description: "نوفر المساعدات الطبية والغذائية والتعليمية للمحتاجين في قطاع غزة"
    },
    {
      title: "بناء مستقبل أفضل",
      subtitle: "تبرعك اليوم هو استثمار في الغد",
      description: "ندعم المشاريع التنموية المستدامة لبناء مستقبل أفضل لأطفال غزة"
    }
  ];

  const stats = [
    { icon: Users, number: "50,000+", label: "مستفيد" },
    { icon: Target, number: "200+", label: "مشروع" },
    { icon: Award, number: "5", label: "سنوات خبرة" }
  ];

  // مشروع عام للتبرع
  const generalProject = {
    id: 0,
    title: "تبرع عام لدعم أهل غزة",
    description: "تبرعك سيُستخدم في أكثر المشاريع احتياجاً لدعم أهل غزة",
    category: "عام"
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const handleDonateClick = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 hero-gradient opacity-90"></div>
        
        {/* Pattern Overlay */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,${encodeURIComponent('<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="#ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="4"/></g></g></svg>')}")`
          }}></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          {/* Main Content */}
          <div className="max-w-4xl mx-auto animate-fade-in">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 font-amiri leading-tight">
              {heroSlides[currentSlide].title}
            </h1>
            <p className="text-xl md:text-2xl mb-4 font-semibold text-gaza-gold">
              {heroSlides[currentSlide].subtitle}
            </p>
            <p className="text-lg md:text-xl mb-12 max-w-2xl mx-auto opacity-90 leading-relaxed">
              {heroSlides[currentSlide].description}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <button 
                onClick={handleDonateClick}
                className="bg-white text-gaza-green hover:bg-gaza-gold hover:text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center gap-2"
              >
                تبرع الآن
                <ArrowLeft className="h-5 w-5" />
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-gaza-green px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-xl">
                تعرف على مشاريعنا
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 animate-scale-in"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <stat.icon className="h-8 w-8 mx-auto mb-3 text-gaza-gold" />
                  <div className="text-2xl font-bold mb-1">{stat.number}</div>
                  <div className="text-sm opacity-80">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Slide Indicators */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 space-x-reverse">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide ? 'bg-white' : 'bg-white/40'
                }`}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Donation Modal */}
      <DonationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        project={generalProject}
      />
    </>
  );
};

export default Hero;
