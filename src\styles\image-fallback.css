/* تحسينات للصور الافتراضية والمحلية */

.project-image {
  transition: opacity 0.3s ease;
}

.project-image.loading {
  opacity: 0.5;
}

.project-image.error {
  opacity: 0;
}

.image-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  transform: scale(1.05);
}

.image-placeholder.small {
  font-size: 0.75rem;
}

.image-placeholder.medium {
  font-size: 1rem;
}

.image-placeholder.large {
  font-size: 1.25rem;
}

/* تحسينات للصور في الجدول */
.table-image-container {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
}

.table-image-placeholder {
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #666;
  font-weight: 500;
}

/* تحسينات للصور في البطاقات */
.card-image-container {
  position: relative;
  width: 100%;
  height: 192px; /* h-48 */
  overflow: hidden;
  border-radius: 8px;
}

.card-image-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  text-align: center;
}

.card-image-placeholder .icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.card-image-placeholder .text {
  font-size: 1.25rem;
}

/* تأثيرات التحميل */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 400% 100%;
  animation: shimmer 1.5s infinite;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .card-image-placeholder .icon {
    font-size: 2rem;
  }
  
  .card-image-placeholder .text {
    font-size: 1rem;
  }
}
