<?php
// إعداد headers للـ JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-HTTP-Method-Override');

// إيقاف عرض الأخطاء في المتصفح
ini_set('display_errors', 0);
error_reporting(E_ALL);

try {
    // اختبار الاتصال بقاعدة البيانات
    require_once 'api/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    // اختبار استعلام بسيط
    $query = "SELECT COUNT(*) as total FROM projects";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // اختبار جلب مشروع محدد
    $testId = 15;
    $query2 = "SELECT * FROM projects WHERE id = :id";
    $stmt2 = $db->prepare($query2);
    $stmt2->bindParam(':id', $testId);
    $stmt2->execute();
    $project = $stmt2->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'الاتصال بقاعدة البيانات ناجح',
        'total_projects' => $result['total'],
        'test_project_exists' => $project ? true : false,
        'test_project_data' => $project,
        'php_version' => phpversion(),
        'pdo_available' => extension_loaded('pdo'),
        'pdo_mysql_available' => extension_loaded('pdo_mysql')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الاتصال: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
