# دليل نظام رفع صور المشاريع

## 🎯 المشكلة التي تم حلها
- استبدال روابط الصور الخارجية برفع صور محلية
- توليد أسماء عشوائية للصور المرفوعة
- حفظ مسار الصورة في جدول projects

## ✅ الحلول المطبقة

### 1. إنشاء مجلد رفع الصور
```
uploads/
├── .htaccess (حماية المجلد الرئيسي)
├── index.php (منع الوصول المباشر)
├── payment_proofs/ (إثبات الدفع)
│   ├── .htaccess
│   └── index.php
└── project_images/ (صور المشاريع - جديد)
    ├── .htaccess
    └── index.php
```

### 2. تحديث API المشاريع (projects.php)

#### دالة توليد اسم عشوائي للصورة:
```php
function generateRandomImageName($originalName) {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $randomName = 'project_' . uniqid() . '_' . time() . '.' . $extension;
    return $randomName;
}
```

#### دالة رفع صورة المشروع:
```php
function uploadProjectImage($file) {
    $uploadDir = '../uploads/project_images/';
    
    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    // التحقق من حجم الملف (10MB كحد أقصى)
    if ($file['size'] > 10 * 1024 * 1024) {
        throw new Exception('حجم الصورة كبير جداً. الحد الأقصى 10MB');
    }
    
    // التحقق من أن الملف صورة فعلاً
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        throw new Exception('الملف المرفوع ليس صورة صالحة');
    }
    
    // توليد اسم عشوائي ورفع الملف
    $fileName = generateRandomImageName($file['name']);
    $filePath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return 'uploads/project_images/' . $fileName;
    }
}
```

#### تحديث دالة createProject:
```php
function createProject($db) {
    // التحقق من البيانات المطلوبة
    if (!isset($_POST['title']) || !isset($_POST['category']) || !isset($_POST['target_amount'])) {
        // خطأ
    }
    
    $image_url = null;
    
    // رفع الصورة إذا كانت موجودة
    if (isset($_FILES['project_image']) && $_FILES['project_image']['error'] === UPLOAD_ERR_OK) {
        $image_url = uploadProjectImage($_FILES['project_image']);
    }
    
    // إدراج المشروع في قاعدة البيانات
    $query = "INSERT INTO projects (title, description, category, target_amount, image_url) 
              VALUES (:title, :description, :category, :target_amount, :image_url)";
}
```

### 3. تحديث AddProject.tsx

#### إضافة حالات جديدة:
```typescript
const [projectImage, setProjectImage] = useState<File | null>(null);
const [imagePreview, setImagePreview] = useState<string | null>(null);
```

#### دالة معالجة تغيير الصورة:
```typescript
const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const file = e.target.files?.[0];
  if (file) {
    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('نوع الملف غير مدعوم. يُسمح بالصور فقط');
      return;
    }
    
    // التحقق من حجم الملف (10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('حجم الصورة كبير جداً. الحد الأقصى 10MB');
      return;
    }
    
    setProjectImage(file);
    
    // إنشاء معاينة للصورة
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }
};
```

#### تحديث إرسال النموذج:
```typescript
const handleSubmit = async (e: React.FormEvent) => {
  // استخدام FormData لدعم رفع الصور
  const formDataToSend = new FormData();
  formDataToSend.append('title', formData.title);
  formDataToSend.append('description', formData.detailedDescription || formData.description);
  formDataToSend.append('category', formData.category);
  formDataToSend.append('target_amount', formData.targetAmount);
  
  // إضافة الصورة إذا كانت موجودة
  if (projectImage) {
    formDataToSend.append('project_image', projectImage);
  }

  const response = await fetch('http://localhost/gaza/api/projects.php', {
    method: 'POST',
    body: formDataToSend // لا نحتاج Content-Type header مع FormData
  });
};
```

### 4. تحديث عرض المشاريع

#### في Projects.tsx:
```typescript
<img
  src={project.image_url ? `http://localhost/gaza/${project.image_url}` : getDefaultImage(project.category)}
  alt={project.title}
  className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
  onError={(e) => {
    // إذا فشل تحميل الصورة المرفوعة، استخدم الصورة الافتراضية
    e.currentTarget.src = getDefaultImage(project.category);
  }}
/>
```

#### في AdminProjects.tsx:
```typescript
<TableCell>
  {project.image_url ? (
    <img
      src={`http://localhost/gaza/${project.image_url}`}
      alt={project.title}
      className="w-12 h-12 object-cover rounded-lg"
      onError={(e) => {
        e.currentTarget.src = 'https://via.placeholder.com/48x48?text=No+Image';
      }}
    />
  ) : (
    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
      <span className="text-xs text-gray-500">لا توجد</span>
    </div>
  )}
</TableCell>
```

## 🔒 الحماية والأمان

### 1. ملف .htaccess في project_images:
```apache
# منع عرض محتويات المجلد
Options -Indexes

# السماح بعرض الصور فقط
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع تنفيذ ملفات PHP
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# تحديد حد أقصى لحجم الملف (10MB)
LimitRequestBody 10485760
```

### 2. التحقق من نوع الملف:
- صور: JPG, JPEG, PNG, GIF, WebP
- حد أقصى: 10MB
- فحص getimagesize() للتأكد من أن الملف صورة فعلاً

### 3. أسماء ملفات عشوائية:
```
project_[uniqid]_[timestamp].[extension]
مثال: project_64a1b2c3d4e5f_1672531200.jpg
```

## 🧪 الاختبار

### 1. اختبار رفع صور المشاريع:
```
http://localhost/gaza/test_project_upload.php
```

### 2. اختبار API مباشرة:
```bash
curl -X POST \
  -F "title=مشروع تجريبي" \
  -F "description=وصف تجريبي" \
  -F "category=طبي" \
  -F "target_amount=10000" \
  -F "project_image=@/path/to/image.jpg" \
  http://localhost/gaza/api/projects.php
```

### 3. اختبار الواجهة:
1. افتح لوحة الإدارة: `http://localhost:5173/admin/projects`
2. اضغط "إضافة مشروع جديد"
3. املأ النموذج وارفع صورة
4. تحقق من حفظ الصورة في المجلد
5. تحقق من عرض الصورة في قائمة المشاريع

## 📁 هيكل الملفات الجديد

```
gaza/
├── uploads/
│   ├── .htaccess
│   ├── index.php
│   ├── payment_proofs/ (إثبات الدفع)
│   └── project_images/ (صور المشاريع - جديد)
│       ├── .htaccess
│       ├── index.php
│       └── project_[id]_[timestamp].[ext] (الصور المرفوعة)
├── api/
│   └── projects.php (محدث)
├── src/components/
│   ├── Projects.tsx (محدث)
│   └── admin/
│       ├── AddProject.tsx (محدث)
│       └── AdminProjects.tsx (محدث)
├── test_project_upload.php (جديد)
└── PROJECT_IMAGES_GUIDE.md (هذا الملف)
```

## 🚀 خطوات التشغيل

1. **تأكد من إعدادات PHP:**
   ```php
   file_uploads = On
   upload_max_filesize = 15M
   post_max_size = 15M
   max_file_uploads = 20
   ```

2. **تأكد من صلاحيات المجلدات:**
   ```bash
   chmod 755 uploads/project_images/
   ```

3. **اختبر النظام:**
   - افتح `http://localhost/gaza/test_project_upload.php`
   - جرب رفع صورة مشروع
   - تحقق من حفظ الصورة في المجلد

4. **اختبر الواجهة:**
   - افتح لوحة الإدارة وأضف مشروع جديد مع صورة
   - تحقق من عرض الصورة في قائمة المشاريع والواجهة الأمامية

## ⚠️ ملاحظات مهمة

1. **أمان الصور:**
   - يتم فحص نوع الملف قبل الرفع
   - يتم توليد أسماء عشوائية لمنع التضارب
   - يتم منع تنفيذ ملفات PHP في مجلد الصور

2. **حجم الصور:**
   - الحد الأقصى 10MB لكل صورة
   - يمكن تعديل الحد في API و .htaccess

3. **أنواع الصور المدعومة:**
   - JPG, JPEG, PNG, GIF, WebP

4. **النسخ الاحتياطي:**
   - احرص على عمل نسخة احتياطية من مجلد uploads
   - الصور المرفوعة لا تُحذف تلقائياً

5. **حذف المشاريع:**
   - عند حذف مشروع، يتم حذف صورته تلقائياً من الخادم

## 🔧 استكشاف الأخطاء

### مشكلة: لا يتم رفع الصورة
- تحقق من صلاحيات المجلد
- تحقق من إعدادات PHP
- تحقق من حجم الصورة

### مشكلة: لا تظهر الصورة في الواجهة
- تحقق من مسار الصورة في قاعدة البيانات
- تحقق من وجود الصورة فعلياً في المجلد

### مشكلة: خطأ في عرض الصورة
- تحقق من مسار الصورة
- تحقق من صلاحيات الوصول للملف
