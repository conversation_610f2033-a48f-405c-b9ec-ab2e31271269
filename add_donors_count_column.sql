-- إضافة عمود donors_count إلى جدول المشاريع إذا لم يكن موجوداً
-- يجب تشغيل هذا الاستعلام في phpMyAdmin أو MySQL command line

-- التحقق من وجود العمود أولاً
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = 'gaza_donations' 
AND table_name = 'projects' 
AND column_name = 'donors_count';

-- إضافة العمود إذا لم يكن موجوداً
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE projects ADD COLUMN donors_count INT NOT NULL DEFAULT 0 AFTER raised_amount', 
    'SELECT "Column donors_count already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- تحديث قيم donors_count بناءً على عدد التبرعات الفعلية
UPDATE projects p 
SET donors_count = (
    SELECT COUNT(*) 
    FROM donations d 
    WHERE d.project_id = p.id
) 
WHERE EXISTS (SELECT 1 FROM donations WHERE project_id = p.id);

-- عرض النتيجة
SELECT 'تم إضافة عمود donors_count وتحديث القيم بنجاح' as result;
