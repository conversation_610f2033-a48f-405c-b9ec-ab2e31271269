
import React from 'react';
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

const Footer = () => {
  const quickLinks = [
    { name: 'الرئيسية', href: '#home' },
    { name: 'المشاريع', href: '#projects' },
    { name: 'من نحن', href: '#about' },
    { name: 'تأثير التبرعات', href: '#impact' },
  ];

  const supportLinks = [
    { name: 'كيف تتبرع', href: '#' },
    { name: 'الأسئلة الشائعة', href: '#' },
    { name: 'التقارير المالية', href: '#' },
    { name: 'سياسة الخصوصية', href: '#' },
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
    { name: 'YouTube', icon: Youtube, href: '#' },
  ];

  const donationMethods = [
    { name: 'البنك الأهلي السعودي', account: '********************' },
    { name: 'stc pay', account: '+************' },
    { name: 'ويسترن يونيون', account: 'Gaza Support Foundation' },
  ];

  return (
    <footer className="bg-gaza-green text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Organization Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-3 mb-6">
              <div className="bg-gaza-gold p-2 rounded-lg">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold font-amiri">دعم غزة</h3>
                <p className="text-sm opacity-80">معاً نبني الأمل</p>
              </div>
            </div>
            <p className="text-white/80 leading-relaxed mb-6">
              منظمة إنسانية تعمل على تقديم المساعدات العاجلة والتنموية لأهل غزة 
              من خلال شبكة واسعة من المتبرعين والشركاء حول العالم.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-gaza-gold" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-gaza-gold" />
                <span className="text-sm">+966 50 123 4567</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-gaza-gold" />
                <span className="text-sm">الرياض، المملكة العربية السعودية</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-bold mb-6 font-amiri">روابط سريعة</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-white/80 hover:text-gaza-gold transition-colors duration-300 hover:translate-x-1 inline-block"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-bold mb-6 font-amiri">الدعم والمساعدة</h4>
            <ul className="space-y-3">
              {supportLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-white/80 hover:text-gaza-gold transition-colors duration-300 hover:translate-x-1 inline-block"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Donation Methods */}
          <div>
            <h4 className="text-lg font-bold mb-6 font-amiri">طرق التبرع</h4>
            <div className="space-y-4">
              {donationMethods.map((method, index) => (
                <div key={index} className="bg-white/10 p-4 rounded-lg">
                  <div className="font-semibold text-gaza-gold mb-1">{method.name}</div>
                  <div className="text-sm text-white/80 font-mono">{method.account}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Social Media & Newsletter */}
        <div className="border-t border-white/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Social Links */}
            <div className="flex items-center gap-4">
              <span className="text-white/80 ml-4">تابعونا:</span>
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="bg-white/10 hover:bg-gaza-gold p-3 rounded-full transition-all duration-300 hover:scale-110"
                  aria-label={social.name}
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div className="flex flex-col sm:flex-row gap-3">
              <input
                type="email"
                placeholder="اشترك في النشرة البريدية"
                className="px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:border-gaza-gold"
              />
              <button className="bg-gaza-gold hover:bg-gaza-gold/80 px-6 py-2 rounded-lg font-semibold transition-colors">
                اشتراك
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-black/20 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-white/80 text-sm">
              © 2024 دعم غزة. جميع الحقوق محفوظة.
            </div>
            
            <div className="flex items-center gap-6 text-sm text-white/80">
              <a href="#" className="hover:text-gaza-gold transition-colors">الشروط والأحكام</a>
              <a href="#" className="hover:text-gaza-gold transition-colors">سياسة الخصوصية</a>
              <a href="#" className="hover:text-gaza-gold transition-colors">اتصل بنا</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
