import React from 'react';
import { X, Calendar, Target, Users, MapPin, Clock, Share2 } from 'lucide-react';

interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  target_amount: number;
  raised_amount: number;
  donors_count: number;
  status: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  percentage: number;
}

interface ProjectViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onDonate?: (project: Project) => void;
}

const ProjectViewModal: React.FC<ProjectViewModalProps> = ({
  isOpen,
  onClose,
  project,
  onDonate
}) => {
  if (!isOpen || !project) return null;

  const getDefaultImage = (category: string) => {
    const images: { [key: string]: string } = {
      'طبي': 'https://images.unsplash.com/photo-1584515933487-779824d29309?w=800&h=600&fit=crop',
      'غذائي': 'https://images.unsplash.com/photo-1593113598332-cd288d649433?w=800&h=600&fit=crop',
      'تعليمي': 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&h=600&fit=crop',
      'إسكان': 'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=800&h=600&fit=crop',
      'مياه': 'https://images.unsplash.com/photo-1541199249251-f713e6145474?w=800&h=600&fit=crop',
      'ملابس': 'https://images.unsplash.com/photo-1516627145497-ae4ef73a69e0?w=800&h=600&fit=crop'
    };
    return images[category] || 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&h=600&fit=crop';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800';
      case 'متوقف':
        return 'bg-red-100 text-red-800';
      case 'عاجل':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: project.title,
        text: project.description,
        url: window.location.href
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ رابط المشروع');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex justify-between items-center rounded-t-2xl">
          <h2 className="text-2xl font-bold text-gaza-green font-amiri">تفاصيل المشروع</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={handleShare}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              title="مشاركة المشروع"
            >
              <Share2 className="h-5 w-5 text-gray-600" />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Image and Basic Info */}
            <div className="space-y-6">
              {/* Project Image */}
              <div className="relative overflow-hidden rounded-xl">
                <img
                  src={project.image_url ? `http://localhost/gaza/${project.image_url}` : getDefaultImage(project.category)}
                  alt={project.title}
                  className="w-full h-64 object-cover"
                  onError={(e) => {
                    e.currentTarget.src = getDefaultImage(project.category);
                  }}
                />
                <div className="absolute top-4 right-4 flex gap-2">
                  <span className="bg-gaza-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {project.category}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>
              </div>

              {/* Progress Section */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-bold text-gaza-green mb-4 font-amiri">تقدم المشروع</h3>
                
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-lg font-bold text-gaza-green">
                      {Number(project.raised_amount).toLocaleString()} ر.س
                    </span>
                    <span className="text-sm text-gray-600">
                      من {Number(project.target_amount).toLocaleString()} ر.س
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-4">
                    <div
                      className="bg-gradient-to-r from-gaza-green to-gaza-gold h-4 rounded-full transition-all duration-500"
                      style={{ width: `${Math.min(project.percentage || 0, 100)}%` }}
                    ></div>
                  </div>
                  <div className="text-center mt-2">
                    <span className="text-2xl font-bold text-gaza-green">
                      {Math.round(project.percentage || 0)}%
                    </span>
                    <span className="text-gray-600 mr-2">مكتمل</span>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-white rounded-lg">
                    <Users className="h-6 w-6 text-gaza-green mx-auto mb-2" />
                    <div className="text-xl font-bold text-gaza-green">{project.donors_count || 0}</div>
                    <div className="text-sm text-gray-600">متبرع</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg">
                    <Target className="h-6 w-6 text-gaza-green mx-auto mb-2" />
                    <div className="text-xl font-bold text-gaza-green">
                      {(Number(project.target_amount) - Number(project.raised_amount)).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">ر.س متبقي</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Details */}
            <div className="space-y-6">
              {/* Project Title and Description */}
              <div>
                <h1 className="text-3xl font-bold text-gaza-green mb-4 font-amiri">
                  {project.title}
                </h1>
                <p className="text-gray-700 leading-relaxed text-lg">
                  {project.description}
                </p>
              </div>

              {/* Project Meta Info */}
              <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                <h3 className="text-lg font-bold text-gaza-green font-amiri">معلومات المشروع</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-gaza-green" />
                    <div>
                      <span className="text-sm text-gray-600">تاريخ الإنشاء:</span>
                      <span className="mr-2 font-semibold">
                        {new Date(project.created_at).toLocaleDateString('ar-SA', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gaza-green" />
                    <div>
                      <span className="text-sm text-gray-600">آخر تحديث:</span>
                      <span className="mr-2 font-semibold">
                        {new Date(project.updated_at).toLocaleDateString('ar-SA', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-gaza-green" />
                    <div>
                      <span className="text-sm text-gray-600">الموقع:</span>
                      <span className="mr-2 font-semibold">قطاع غزة، فلسطين</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Donation Button */}
              {onDonate && project.status === 'نشط' && (
                <button
                  onClick={() => onDonate(project)}
                  className="w-full btn-primary text-lg py-4 flex items-center justify-center gap-3 group"
                >
                  <Target className="h-5 w-5" />
                  تبرع الآن لهذا المشروع
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectViewModal;
