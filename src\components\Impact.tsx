
import React from 'react';
import { Heart, Users, Home, GraduationCap, Stethoscope, Droplets } from 'lucide-react';

const Impact = () => {
  const impactStats = [
    {
      icon: Users,
      number: "50,000+",
      label: "شخص استفاد من مساعداتنا",
      description: "عائلات وأفراد حصلوا على الدعم المباشر"
    },
    {
      icon: Home,
      number: "2,500",
      label: "عائلة حصلت على مأوى",
      description: "توفير السكن المؤقت والدائم للأسر المحتاجة"
    },
    {
      icon: GraduationCap,
      number: "8,000",
      label: "طفل عاد للمدرسة",
      description: "دعم التعليم وتوفير المستلزمات الدراسية"
    },
    {
      icon: Stethoscope,
      number: "15,000",
      label: "خدمة طبية قُدمت",
      description: "علاجات وأدوية ومعدات طبية"
    },
    {
      icon: Droplets,
      number: "100",
      label: "بئر مياه حُفر",
      description: "مصادر مياه نظيفة للمجتمعات المحتاجة"
    },
    {
      icon: Heart,
      number: "500",
      label: "يتيم تم كفالته",
      description: "رعاية شاملة للأيتام والأرامل"
    }
  ];

  const successStories = [
    {
      name: "عائلة أبو محمد",
      story: "بعد فقدان منزلهم، تمكنت عائلة أبو محمد من الحصول على سكن مؤقت آمن لحين إعادة البناء",
      image: "https://images.unsplash.com/photo-1509909756405-be0199881695?w=400&h=300&fit=crop",
      category: "إسكان"
    },
    {
      name: "مدرسة الأمل",
      story: "تم تأهيل مدرسة الأمل وتوفير المستلزمات التعليمية لـ 300 طالب وطالبة",
      image: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400&h=300&fit=crop",
      category: "تعليم"
    },
    {
      name: "عيادة الشفاء",
      story: "توفير الأدوية والمعدات الطبية الأساسية لعيادة الشفاء لخدمة أكثر من 1000 مريض شهرياً",
      image: "https://images.unsplash.com/photo-1584515933487-779824d29309?w=400&h=300&fit=crop",
      category: "صحة"
    }
  ];

  return (
    <section id="impact" className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gaza-green font-amiri">
            تأثير تبرعاتكم
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            نفتخر بالإنجازات التي حققناها معاً بفضل تبرعاتكم الكريمة. كل ريال تبرعتم به ترك أثراً إيجابياً في حياة الآلاف
          </p>
        </div>

        {/* Impact Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {impactStats.map((stat, index) => (
            <div
              key={index}
              className="bg-white p-8 rounded-2xl shadow-lg card-hover animate-fade-in text-center"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="bg-gradient-to-r from-gaza-green to-gaza-gold p-4 rounded-full w-16 h-16 mx-auto mb-6 flex items-center justify-center">
                <stat.icon className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-gaza-green mb-2 font-amiri">
                {stat.number}
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-3">
                {stat.label}
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {stat.description}
              </p>
            </div>
          ))}
        </div>

        {/* Success Stories */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gaza-green font-amiri mb-4">
              قصص نجاح ملهمة
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              هذه بعض القصص الحقيقية التي تُظهر التأثير الإيجابي لتبرعاتكم في حياة الناس
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {successStories.map((story, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl overflow-hidden shadow-lg card-hover animate-fade-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative overflow-hidden h-48">
                  <img
                    src={story.image}
                    alt={story.name}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                  />
                  <div className="absolute top-4 right-4 bg-gaza-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {story.category}
                  </div>
                </div>
                <div className="p-6">
                  <h4 className="text-xl font-bold text-gaza-green mb-3 font-amiri">
                    {story.name}
                  </h4>
                  <p className="text-muted-foreground leading-relaxed">
                    {story.story}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-gaza-green to-gaza-gold text-white p-8 md:p-12 rounded-2xl text-center animate-fade-in">
          <Heart className="h-16 w-16 mx-auto mb-6 text-gaza-gold" />
          <h3 className="text-3xl font-bold mb-4 font-amiri">
            كن جزءاً من القصة القادمة
          </h3>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            تبرعك اليوم سيكون بداية قصة نجاح جديدة. انضم إلينا في صنع الفرق وبناء الأمل
          </p>
          <button className="bg-white text-gaza-green hover:bg-gaza-gold hover:text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
            ابدأ رحلة التبرع الآن
          </button>
        </div>
      </div>
    </section>
  );
};

export default Impact;
