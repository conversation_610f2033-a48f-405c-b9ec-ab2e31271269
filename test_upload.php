<?php
// اختبار رفع الملفات
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع إثبات الدفع</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; max-width: 400px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #218838; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>اختبار رفع إثبات الدفع</h1>
    
    <form action="api/donations.php" method="POST" enctype="multipart/form-data">
        <div class="form-group">
            <label>معرف المشروع:</label>
            <input type="number" name="project_id" value="1" required>
        </div>
        
        <div class="form-group">
            <label>اسم المتبرع:</label>
            <input type="text" name="donor_name" value="متبرع تجريبي" required>
        </div>
        
        <div class="form-group">
            <label>البريد الإلكتروني:</label>
            <input type="email" name="donor_email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label>رقم الهاتف:</label>
            <input type="text" name="donor_phone" value="01234567890" required>
        </div>
        
        <div class="form-group">
            <label>الدولة:</label>
            <input type="text" name="donor_country" value="مصر" required>
        </div>
        
        <div class="form-group">
            <label>المبلغ:</label>
            <input type="number" name="amount" value="100" step="0.01" required>
        </div>
        
        <div class="form-group">
            <label>العملة:</label>
            <select name="currency" required>
                <option value="EGP">جنيه مصري</option>
                <option value="USD">دولار أمريكي</option>
                <option value="EUR">يورو</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>الرسالة:</label>
            <textarea name="message" rows="3">رسالة تجريبية</textarea>
        </div>
        
        <div class="form-group">
            <label>متبرع مجهول:</label>
            <input type="checkbox" name="is_anonymous" value="1" style="width: auto;">
        </div>
        
        <div class="form-group">
            <label>إثبات الدفع:</label>
            <input type="file" name="payment_proof" accept="image/*,.pdf">
            <small>يمكنك رفع صورة أو ملف PDF (حد أقصى 5MB)</small>
        </div>
        
        <input type="hidden" name="status" value="pending">
        
        <button type="submit">إرسال التبرع</button>
    </form>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo '<div class="result">';
        echo '<h3>نتيجة الاختبار:</h3>';
        
        // عرض البيانات المرسلة
        echo '<h4>البيانات المرسلة:</h4>';
        echo '<pre>' . print_r($_POST, true) . '</pre>';
        
        // عرض معلومات الملف
        if (isset($_FILES['payment_proof'])) {
            echo '<h4>معلومات الملف:</h4>';
            echo '<pre>' . print_r($_FILES['payment_proof'], true) . '</pre>';
        }
        
        echo '</div>';
    }
    ?>
    
    <hr>
    
    <h2>اختبار مجلد الرفع</h2>
    <?php
    $uploadDir = 'uploads/payment_proofs/';
    if (is_dir($uploadDir)) {
        echo '<p style="color: green;">✅ مجلد الرفع موجود: ' . $uploadDir . '</p>';
        
        if (is_writable($uploadDir)) {
            echo '<p style="color: green;">✅ مجلد الرفع قابل للكتابة</p>';
        } else {
            echo '<p style="color: red;">❌ مجلد الرفع غير قابل للكتابة</p>';
        }
        
        // عرض الملفات الموجودة
        $files = scandir($uploadDir);
        $files = array_filter($files, function($file) {
            return !in_array($file, ['.', '..', 'index.php']);
        });
        
        if (!empty($files)) {
            echo '<h4>الملفات الموجودة:</h4>';
            echo '<ul>';
            foreach ($files as $file) {
                $filePath = $uploadDir . $file;
                $fileSize = filesize($filePath);
                $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
                echo '<li>';
                echo '<strong>' . $file . '</strong> ';
                echo '(' . number_format($fileSize / 1024, 2) . ' KB) ';
                echo '<small>' . $fileTime . '</small>';
                echo ' <a href="' . $filePath . '" target="_blank">عرض</a>';
                echo '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>لا توجد ملفات مرفوعة</p>';
        }
    } else {
        echo '<p style="color: red;">❌ مجلد الرفع غير موجود: ' . $uploadDir . '</p>';
    }
    ?>
    
    <h2>إعدادات PHP للرفع</h2>
    <ul>
        <li><strong>file_uploads:</strong> <?php echo ini_get('file_uploads') ? 'مفعل' : 'معطل'; ?></li>
        <li><strong>upload_max_filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
        <li><strong>post_max_size:</strong> <?php echo ini_get('post_max_size'); ?></li>
        <li><strong>max_file_uploads:</strong> <?php echo ini_get('max_file_uploads'); ?></li>
        <li><strong>memory_limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
    </ul>
</body>
</html>
