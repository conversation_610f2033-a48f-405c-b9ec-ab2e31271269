<?php
// فحص syntax ملف projects.php
header('Content-Type: text/plain; charset=utf-8');

echo "=== فحص syntax ملف api/projects.php ===\n";

// فحص syntax
$output = [];
$return_var = 0;
exec('php -l api/projects.php 2>&1', $output, $return_var);

echo "نتيجة فحص syntax:\n";
echo implode("\n", $output) . "\n";
echo "كود الإرجاع: " . $return_var . "\n";

if ($return_var === 0) {
    echo "✅ لا توجد أخطاء syntax\n";
    
    // محاولة تشغيل الملف مع GET request
    echo "\n=== اختبار تشغيل الملف ===\n";
    
    // تعيين متغيرات البيئة للاختبار
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    ob_start();
    try {
        include 'api/projects.php';
        $output = ob_get_contents();
        echo "✅ تم تشغيل الملف بنجاح\n";
        echo "الإخراج:\n" . $output . "\n";
    } catch (Exception $e) {
        echo "❌ خطأ في تشغيل الملف: " . $e->getMessage() . "\n";
        echo "الملف: " . $e->getFile() . "\n";
        echo "السطر: " . $e->getLine() . "\n";
    } catch (Error $e) {
        echo "❌ خطأ فادح: " . $e->getMessage() . "\n";
        echo "الملف: " . $e->getFile() . "\n";
        echo "السطر: " . $e->getLine() . "\n";
    }
    ob_end_clean();
    
} else {
    echo "❌ توجد أخطاء syntax في الملف\n";
}

echo "\n=== انتهى الفحص ===\n";
?>
