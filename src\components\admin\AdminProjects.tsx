
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Edit, Trash2, Eye, Search, RefreshCw } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';

interface Project {
  id: number;
  title: string;
  description?: string;
  category: string;
  target_amount: number;
  raised_amount: number;
  status: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  percentage: number;
}

const AdminProjects = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // حالات المعاينة والتعديل
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<Project>>({});
  const [editLoading, setEditLoading] = useState(false);
  const [newImageFile, setNewImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // جلب المشاريع من قاعدة البيانات
  const fetchProjects = async (page = 1) => {
    try {
      setLoading(true);

      // بناء معاملات URL
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (categoryFilter) params.append('category', categoryFilter);
      if (statusFilter) params.append('status', statusFilter);

      const response = await fetch(`http://localhost/gaza/api/projects.php?${params}`);
      const data = await response.json();

      if (data.success) {
        setProjects(data.data);
        setCurrentPage(data.pagination.current_page);
        setTotalPages(data.pagination.total_pages);
        setTotalRecords(data.pagination.total_records);
        setError(null);
      } else {
        setError(data.message || 'خطأ في جلب المشاريع');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  // فتح معاينة المشروع
  const handleViewProject = (project: Project) => {
    setSelectedProject(project);
    setViewDialogOpen(true);
  };

  // فتح تعديل المشروع
  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setEditFormData({
      title: project.title,
      description: project.description,
      category: project.category,
      target_amount: project.target_amount,
      raised_amount: project.raised_amount,
      status: project.status
    });
    // إعادة تعيين حالة الصورة
    setNewImageFile(null);
    setImagePreview(null);
    setEditDialogOpen(true);
  };

  // التعامل مع تغيير الصورة
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // التحقق من نوع الملف
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يُسمح بالصور فقط (JPG, PNG, GIF, WebP)');
        return;
      }

      // التحقق من حجم الملف (10MB كحد أقصى)
      if (file.size > 10 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً. الحد الأقصى 10MB');
        return;
      }

      setNewImageFile(file);

      // إنشاء معاينة للصورة
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // حفظ تعديل المشروع
  const handleSaveEdit = async () => {
    if (!selectedProject) {
      alert('لم يتم تحديد مشروع للتعديل');
      return;
    }

    if (!editFormData.title?.trim() || !editFormData.category?.trim() || !editFormData.target_amount || editFormData.target_amount <= 0) {
      alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح (العنوان، الفئة، المبلغ المستهدف)');
      return;
    }

    setEditLoading(true);
    try {
      const formData = new FormData();

      // إضافة البيانات الأساسية
      formData.append('id', selectedProject.id.toString());
      formData.append('title', editFormData.title || '');
      formData.append('description', editFormData.description || '');
      formData.append('category', editFormData.category || '');
      formData.append('target_amount', editFormData.target_amount?.toString() || '0');
      formData.append('raised_amount', editFormData.raised_amount?.toString() || selectedProject.raised_amount.toString());
      formData.append('status', editFormData.status || 'نشط');
      formData.append('current_image_url', selectedProject.image_url || '');

      // إضافة الصورة الجديدة إذا تم اختيارها
      if (newImageFile) {
        formData.append('project_image', newImageFile);
      }

      // طباعة البيانات للتشخيص
      console.log('Sending data:', {
        id: selectedProject.id,
        title: editFormData.title,
        category: editFormData.category,
        target_amount: editFormData.target_amount,
        hasNewImage: !!newImageFile
      });

      // إرسال طلب POST (سيتم التعامل معه كتحديث لوجود ID)
      const response = await fetch('http://localhost/gaza/api/projects.php', {
        method: 'POST',
        body: formData
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const data = await response.json();
      console.log('Response data:', data);

      if (data.success) {
        alert('تم تحديث المشروع بنجاح');
        setEditDialogOpen(false);
        setNewImageFile(null);
        setImagePreview(null);
        fetchProjects(currentPage);
      } else {
        alert(data.message || 'خطأ في تحديث المشروع');
      }
    } catch (err) {
      console.error('Error updating project:', err);
      alert('خطأ في الاتصال بالخادم: ' + (err instanceof Error ? err.message : 'خطأ غير معروف'));
    } finally {
      setEditLoading(false);
    }
  };

  // حذف مشروع
  const deleteProject = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost/gaza/api/projects.php?id=${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        alert('تم حذف المشروع بنجاح');
        fetchProjects(); // إعادة جلب المشاريع
      } else {
        alert(data.message || 'خطأ في حذف المشروع');
      }
    } catch (err) {
      alert('خطأ في الاتصال بالخادم');
      console.error('Error deleting project:', err);
    }
  };

  useEffect(() => {
    fetchProjects(1);
  }, []);

  // إعادة البحث عند تغيير الفلاتر
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProjects(1);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, categoryFilter, statusFilter]);

  // لا نحتاج للفلترة المحلية لأننا نستخدم فلترة من الخادم

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'متوقف':
        return 'bg-red-100 text-red-800';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-amiri">جاري تحميل المشاريع...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 ml-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-semibold">خطأ في تحميل المشاريع</h3>
                <p className="text-red-600">{error}</p>
              </div>
            </div>
            <button
              onClick={() => fetchProjects(1)}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إدارة المشاريع</h2>
          <p className="text-gray-600">إدارة جميع مشاريع التبرع ({projects.length} مشروع)</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => fetchProjects(1)}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            تحديث
          </button>
          <Link
            to="/admin/projects/add"
            className="bg-gaza-green text-white px-4 py-2 rounded-lg hover:bg-gaza-green/90 transition-colors flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            إضافة مشروع جديد
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* البحث */}
          <div className="relative">
            <Search className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="البحث في المشاريع..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>

          {/* فلتر الفئة */}
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
          >
            <option value="">جميع الفئات</option>
            <option value="طبي">طبي</option>
            <option value="غذائي">غذائي</option>
            <option value="تعليمي">تعليمي</option>
            <option value="إسكان">إسكان</option>
            <option value="مياه">مياه</option>
            <option value="ملابس">ملابس</option>
            <option value="كهرباء">كهرباء</option>
            <option value="أخرى">أخرى</option>
          </select>

          {/* فلتر الحالة */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
          >
            <option value="">جميع الحالات</option>
            <option value="نشط">نشط</option>
            <option value="مكتمل">مكتمل</option>
            <option value="متوقف">متوقف</option>
            <option value="عاجل">عاجل</option>
          </select>

          {/* زر البحث */}
          <button
            onClick={() => fetchProjects(1)}
            className="bg-gaza-green text-white px-4 py-2 rounded-lg hover:bg-gaza-green/90 transition-colors flex items-center justify-center gap-2"
          >
            <Search className="h-4 w-4" />
            بحث
          </button>
        </div>
      </div>

      {/* Projects Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">الصورة</TableHead>
              <TableHead className="text-right">اسم المشروع</TableHead>
              <TableHead className="text-right">الفئة</TableHead>
              <TableHead className="text-right">الهدف</TableHead>
              <TableHead className="text-right">المُجمع</TableHead>
              <TableHead className="text-right">النسبة</TableHead>
              <TableHead className="text-right">الحالة</TableHead>
              <TableHead className="text-right">تاريخ الإنشاء</TableHead>
              <TableHead className="text-right">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.map((project) => {
              const percentage = project.percentage || Math.round((project.raised_amount / project.target_amount) * 100);
              return (
                <TableRow key={project.id}>
                  <TableCell>
                    {project.image_url ? (
                      <img
                        src={`http://localhost/gaza/${project.image_url}`}
                        alt={project.title}
                        className="w-12 h-12 object-cover rounded-lg"
                        onError={(e) => {
                          // إخفاء الصورة وإظهار placeholder محلي بدلاً من استخدام via.placeholder.com
                          e.currentTarget.style.display = 'none';
                          const placeholder = e.currentTarget.nextElementSibling as HTMLElement;
                          if (placeholder) {
                            placeholder.style.display = 'flex';
                          }
                        }}
                      />
                    ) : null}

                    {/* Placeholder محلي للصور المفقودة */}
                    <div
                      className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center"
                      style={{ display: project.image_url ? 'none' : 'flex' }}
                    >
                      <span className="text-xs text-gray-500">لا توجد</span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{project.title}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                      {project.category}
                    </span>
                  </TableCell>
                  <TableCell>{Number(project.target_amount).toLocaleString()} ر.س</TableCell>
                  <TableCell>{Number(project.raised_amount).toLocaleString()} ر.س</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gaza-green h-2 rounded-full"
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs">{percentage}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </TableCell>
                  <TableCell>{new Date(project.created_at).toLocaleDateString('ar-SA')}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="عرض التفاصيل"
                        onClick={() => handleViewProject(project)}
                      >
                        <Eye className="h-4 w-4 text-gray-600" />
                      </button>
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="تعديل المشروع"
                        onClick={() => handleEditProject(project)}
                      >
                        <Edit className="h-4 w-4 text-blue-600" />
                      </button>
                      <button
                        className="p-1 hover:bg-gray-100 rounded"
                        title="حذف المشروع"
                        onClick={() => deleteProject(project.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {projects.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                  {searchTerm ? 'لا توجد مشاريع تطابق البحث' : 'لا توجد مشاريع'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                عرض {((currentPage - 1) * 10) + 1} إلى {Math.min(currentPage * 10, totalRecords)} من {totalRecords} مشروع
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => fetchProjects(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum: number;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => fetchProjects(pageNum)}
                        className={`px-3 py-1 text-sm border rounded-lg ${
                          currentPage === pageNum
                            ? 'bg-gaza-green text-white border-gaza-green'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => fetchProjects(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* معاينة المشروع */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-gaza-green font-amiri">
              معاينة المشروع
            </DialogTitle>
          </DialogHeader>

          {selectedProject && (
            <div className="space-y-6">
              {/* صورة المشروع */}
              {selectedProject.image_url && (
                <div className="w-full h-48 rounded-lg overflow-hidden">
                  <img
                    src={`http://localhost/gaza/${selectedProject.image_url}`}
                    alt={selectedProject.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* معلومات المشروع */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-semibold text-gray-700">اسم المشروع</Label>
                  <p className="mt-1 text-gray-900 font-amiri">{selectedProject.title}</p>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">الفئة</Label>
                  <Badge variant="secondary" className="mt-1">
                    {selectedProject.category}
                  </Badge>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">المبلغ المستهدف</Label>
                  <p className="mt-1 text-gaza-green font-bold">
                    {Number(selectedProject.target_amount).toLocaleString()} ر.س
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">المبلغ المُجمع</Label>
                  <p className="mt-1 text-gaza-green font-bold">
                    {Number(selectedProject.raised_amount).toLocaleString()} ر.س
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">نسبة الإنجاز</Label>
                  <div className="mt-1">
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gaza-green h-3 rounded-full"
                        style={{ width: `${Math.min(selectedProject.percentage || 0, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {Math.round(selectedProject.percentage || 0)}% مكتمل
                    </p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">الحالة</Label>
                  <Badge className={`mt-1 ${getStatusColor(selectedProject.status)}`}>
                    {selectedProject.status}
                  </Badge>
                </div>
              </div>

              {/* وصف المشروع */}
              {selectedProject.description && (
                <div>
                  <Label className="text-sm font-semibold text-gray-700">وصف المشروع</Label>
                  <p className="mt-1 text-gray-900 leading-relaxed font-amiri">
                    {selectedProject.description}
                  </p>
                </div>
              )}

              {/* تواريخ */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <Label className="text-sm font-semibold text-gray-700">تاريخ الإنشاء</Label>
                  <p className="mt-1 text-gray-600">
                    {new Date(selectedProject.created_at).toLocaleDateString('ar-SA')}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-semibold text-gray-700">آخر تحديث</Label>
                  <p className="mt-1 text-gray-600">
                    {new Date(selectedProject.updated_at).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* تعديل المشروع */}
      <Dialog open={editDialogOpen} onOpenChange={(open) => {
        setEditDialogOpen(open);
        if (!open) {
          setNewImageFile(null);
          setImagePreview(null);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-gaza-green font-amiri">
              تعديل المشروع
            </DialogTitle>
          </DialogHeader>

          {selectedProject && (
            <div className="space-y-4">
              {/* اسم المشروع */}
              <div>
                <Label htmlFor="edit-title" className="text-sm font-semibold text-gray-700">
                  اسم المشروع *
                </Label>
                <Input
                  id="edit-title"
                  value={editFormData.title || ''}
                  onChange={(e) => setEditFormData({...editFormData, title: e.target.value})}
                  className="mt-1"
                  placeholder="أدخل اسم المشروع"
                />
              </div>

              {/* وصف المشروع */}
              <div>
                <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-700">
                  وصف المشروع
                </Label>
                <Textarea
                  id="edit-description"
                  value={editFormData.description || ''}
                  onChange={(e) => setEditFormData({...editFormData, description: e.target.value})}
                  className="mt-1"
                  placeholder="أدخل وصف المشروع"
                  rows={3}
                />
              </div>

              {/* الفئة والحالة */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-category" className="text-sm font-semibold text-gray-700">
                    الفئة *
                  </Label>
                  <select
                    id="edit-category"
                    value={editFormData.category || ''}
                    onChange={(e) => setEditFormData({...editFormData, category: e.target.value})}
                    className="mt-1 w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
                  >
                    <option value="">اختر الفئة</option>
                    <option value="طبي">طبي</option>
                    <option value="غذائي">غذائي</option>
                    <option value="تعليمي">تعليمي</option>
                    <option value="إسكان">إسكان</option>
                    <option value="مياه">مياه</option>
                    <option value="ملابس">ملابس</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="أخرى">أخرى</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="edit-status" className="text-sm font-semibold text-gray-700">
                    الحالة
                  </Label>
                  <select
                    id="edit-status"
                    value={editFormData.status || ''}
                    onChange={(e) => setEditFormData({...editFormData, status: e.target.value})}
                    className="mt-1 w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
                  >
                    <option value="نشط">نشط</option>
                    <option value="مكتمل">مكتمل</option>
                    <option value="متوقف">متوقف</option>
                    <option value="عاجل">عاجل</option>
                  </select>
                </div>
              </div>

              {/* المبالغ */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-target" className="text-sm font-semibold text-gray-700">
                    المبلغ المستهدف (ر.س) *
                  </Label>
                  <Input
                    id="edit-target"
                    type="number"
                    value={editFormData.target_amount || ''}
                    onChange={(e) => setEditFormData({...editFormData, target_amount: Number(e.target.value)})}
                    className="mt-1"
                    placeholder="0"
                    min="1"
                  />
                </div>

                <div>
                  <Label htmlFor="edit-raised" className="text-sm font-semibold text-gray-700">
                    المبلغ المُجمع (ر.س)
                  </Label>
                  <Input
                    id="edit-raised"
                    type="number"
                    value={editFormData.raised_amount || ''}
                    onChange={(e) => setEditFormData({...editFormData, raised_amount: Number(e.target.value)})}
                    className="mt-1"
                    placeholder="0"
                    min="0"
                  />
                </div>
              </div>

              {/* إدارة الصورة */}
              <div className="space-y-4">
                <Label className="text-sm font-semibold text-gray-700">صورة المشروع</Label>

                {/* الصورة الحالية أو المعاينة */}
                <div className="flex items-start gap-4">
                  {/* الصورة الحالية */}
                  {selectedProject.image_url && !imagePreview && (
                    <div>
                      <p className="text-xs text-gray-600 mb-2">الصورة الحالية:</p>
                      <div className="w-32 h-24 rounded-lg overflow-hidden border">
                        <img
                          src={`http://localhost/gaza/${selectedProject.image_url}`}
                          alt={selectedProject.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {/* معاينة الصورة الجديدة */}
                  {imagePreview && (
                    <div>
                      <p className="text-xs text-gray-600 mb-2">الصورة الجديدة:</p>
                      <div className="w-32 h-24 rounded-lg overflow-hidden border">
                        <img
                          src={imagePreview}
                          alt="معاينة الصورة الجديدة"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* حقل رفع الصورة */}
                <div>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    اختر صورة جديدة (اختياري) - الأنواع المدعومة: JPG, PNG, GIF, WebP - الحد الأقصى: 10MB
                  </p>
                </div>

                {/* أزرار إدارة الصورة */}
                {(imagePreview || newImageFile) && (
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNewImageFile(null);
                        setImagePreview(null);
                        // إعادة تعيين حقل الملف
                        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                        if (fileInput) fileInput.value = '';
                      }}
                    >
                      إلغاء الصورة الجديدة
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setEditDialogOpen(false);
                setNewImageFile(null);
                setImagePreview(null);
              }}
              disabled={editLoading}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleSaveEdit}
              disabled={editLoading}
              className="bg-gaza-green hover:bg-gaza-green/90"
            >
              {editLoading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminProjects;
