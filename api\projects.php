<?php
// إعداد headers للـ JSON
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-HTTP-Method-Override');

// إيقاف عرض الأخطاء في المتصفح
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];

    switch($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                getProject($db, $_GET['id']);
            } else {
                getProjects($db);
            }
            break;
        case 'POST':
            if (isset($_POST['id'])) {
                updateProject($db);
            } else {
                createProject($db);
            }
            break;
        case 'DELETE':
            deleteProject($db);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'طريقة غير مدعومة'
            ], JSON_UNESCAPED_UNICODE);
    }

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// دالة جلب جميع المشاريع مع pagination والفلترة
function getProjects($db) {
    try {
        // معاملات الصفحة والفلترة
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 10;
        $offset = ($page - 1) * $limit;

        // معاملات البحث والفلترة
        $search = $_GET['search'] ?? '';
        $category = $_GET['category'] ?? '';
        $status = $_GET['status'] ?? '';

        // بناء شروط WHERE
        $whereConditions = [];
        $params = [];

        if (!empty($search)) {
            $whereConditions[] = "(title LIKE :search OR description LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }

        if (!empty($category)) {
            $whereConditions[] = "category = :category";
            $params[':category'] = $category;
        }

        if (!empty($status)) {
            $whereConditions[] = "status = :status";
            $params[':status'] = $status;
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // استعلام العد الإجمالي
        $countQuery = "SELECT COUNT(*) as total FROM projects $whereClause";
        $countStmt = $db->prepare($countQuery);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // استعلام البيانات مع Pagination
        $query = "SELECT
            id,
            title,
            description,
            category,
            target_amount,
            raised_amount,
            donors_count,
            status,
            image_url,
            created_at,
            updated_at,
            ROUND((raised_amount / target_amount) * 100, 2) as percentage
        FROM projects
        $whereClause
        ORDER BY created_at DESC
        LIMIT :limit OFFSET :offset";

        $stmt = $db->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب معلومات الصفحات
        $totalPages = ceil($totalRecords / $limit);

        echo json_encode([
            'success' => true,
            'data' => $projects,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_records' => $totalRecords,
                'per_page' => $limit
            ]
        ], JSON_UNESCAPED_UNICODE);

    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب المشاريع: ' . $exception->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

// دالة للحصول على مشروع واحد
function getProject($db, $id) {
    try {
        $query = "SELECT
            id,
            title,
            description,
            category,
            target_amount,
            raised_amount,
            donors_count,
            status,
            image_url,
            created_at,
            updated_at,
            ROUND((raised_amount / target_amount) * 100, 2) as percentage
        FROM projects
        WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        $project = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($project) {
            echo json_encode([
                'success' => true,
                'data' => $project
            ], JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'المشروع غير موجود'
            ], JSON_UNESCAPED_UNICODE);
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب المشروع: ' . $exception->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

// دالة توليد اسم عشوائي للصورة
function generateRandomImageName($originalName) {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $randomName = 'project_' . uniqid() . '_' . time() . '.' . $extension;
    return $randomName;
}

// دالة رفع صورة المشروع
function uploadProjectImage($file) {
    $uploadDir = '../uploads/project_images/';

    // التحقق من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];

    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('نوع الملف غير مدعوم. يُسمح بالصور فقط (JPG, PNG, GIF, WebP)');
    }

    // التحقق من حجم الملف (10MB كحد أقصى)
    if ($file['size'] > 10 * 1024 * 1024) {
        throw new Exception('حجم الصورة كبير جداً. الحد الأقصى 10MB');
    }

    // التحقق من أن الملف صورة فعلاً
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        throw new Exception('الملف المرفوع ليس صورة صالحة');
    }

    // توليد اسم عشوائي ورفع الملف
    $fileName = generateRandomImageName($file['name']);
    $filePath = $uploadDir . $fileName;

    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return 'uploads/project_images/' . $fileName;
    } else {
        throw new Exception('فشل في رفع الصورة');
    }
}

function createProject($db) {
    try {
        // التحقق من البيانات المطلوبة
        if (!isset($_POST['title']) || !isset($_POST['category']) || !isset($_POST['target_amount'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'بيانات غير مكتملة'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }

        $image_url = null;

        // رفع الصورة إذا كانت موجودة
        if (isset($_FILES['project_image']) && $_FILES['project_image']['error'] === UPLOAD_ERR_OK) {
            $image_url = uploadProjectImage($_FILES['project_image']);
        }

        $query = "INSERT INTO projects (title, description, category, target_amount, raised_amount, donors_count, status, image_url)
                  VALUES (:title, :description, :category, :target_amount, :raised_amount, :donors_count, :status, :image_url)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':title', $_POST['title']);
        $stmt->bindParam(':description', $_POST['description'] ?? '');
        $stmt->bindParam(':category', $_POST['category']);
        $stmt->bindParam(':target_amount', $_POST['target_amount']);
        $stmt->bindParam(':raised_amount', $_POST['raised_amount'] ?? 0);
        $stmt->bindParam(':donors_count', $_POST['donors_count'] ?? 0);
        $stmt->bindParam(':status', $_POST['status'] ?? 'نشط');
        $stmt->bindParam(':image_url', $image_url);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم إنشاء المشروع بنجاح',
                'id' => $db->lastInsertId()
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('فشل في إنشاء المشروع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

function updateProject($db) {
    try {
        // تسجيل محاولة التحديث
        error_log("Attempting to update project ID: " . ($_POST['id'] ?? 'not set'));

        // التحقق من معرف المشروع
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'معرف المشروع مطلوب - المستلم: ' . (isset($_POST['id']) ? $_POST['id'] : 'غير موجود')
            ], JSON_UNESCAPED_UNICODE);
            return;
        }

        $project_id = $_POST['id'];
        $image_url = $_POST['current_image_url'] ?? null; // الاحتفاظ بالصورة الحالية

        // التحقق من البيانات المطلوبة
        if (empty($_POST['title']) || empty($_POST['category']) || empty($_POST['target_amount'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'البيانات المطلوبة غير مكتملة (العنوان، الفئة، المبلغ المستهدف)'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }

        // رفع صورة جديدة إذا كانت موجودة
        if (isset($_FILES['project_image']) && $_FILES['project_image']['error'] === UPLOAD_ERR_OK) {
            try {
                // حذف الصورة القديمة إذا كانت موجودة
                if ($image_url && file_exists($image_url)) {
                    unlink($image_url);
                }

                $image_url = uploadProjectImage($_FILES['project_image']);
                error_log("New image uploaded: " . $image_url);
            } catch (Exception $imageException) {
                error_log("Image upload error: " . $imageException->getMessage());
                // الاستمرار بدون تحديث الصورة إذا فشل الرفع
                error_log("Continuing update without image change");
            }
        }

        // الحصول على القيم الاختيارية
        $raised_amount = isset($_POST['raised_amount']) ? floatval($_POST['raised_amount']) : 0;
        $donors_count = isset($_POST['donors_count']) ? intval($_POST['donors_count']) : 0;

        // تسجيل البيانات للتشخيص
        error_log("Update data: ID=" . $project_id . ", Title=" . $_POST['title'] . ", Raised=" . $raised_amount . ", Donors=" . $donors_count);

        $query = "UPDATE projects SET
                  title = :title,
                  description = :description,
                  category = :category,
                  target_amount = :target_amount,
                  raised_amount = :raised_amount,
                  donors_count = :donors_count,
                  status = :status,
                  image_url = :image_url,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $project_id);
        $stmt->bindParam(':title', $_POST['title']);
        $stmt->bindParam(':description', $_POST['description']);
        $stmt->bindParam(':category', $_POST['category']);
        $stmt->bindParam(':target_amount', $_POST['target_amount']);
        $stmt->bindParam(':raised_amount', $raised_amount);
        $stmt->bindParam(':donors_count', $donors_count);
        $stmt->bindParam(':status', $_POST['status'] ?? 'نشط');
        $stmt->bindParam(':image_url', $image_url);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المشروع بنجاح',
                'image_url' => $image_url
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // الحصول على معلومات الخطأ من PDO
            $errorInfo = $stmt->errorInfo();
            throw new Exception('فشل في تحديث المشروع: ' . $errorInfo[2]);
        }
    } catch(Exception $exception) {
        error_log("Update project error: " . $exception->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

function deleteProject($db) {
    $id = $_GET['id'] ?? null;

    if (!$id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المشروع مطلوب'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }

    try {
        // جلب معلومات المشروع أولاً لحذف الصورة
        $selectQuery = "SELECT image_url FROM projects WHERE id = :id";
        $selectStmt = $db->prepare($selectQuery);
        $selectStmt->bindParam(':id', $id);
        $selectStmt->execute();
        $project = $selectStmt->fetch(PDO::FETCH_ASSOC);

        if (!$project) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'المشروع غير موجود'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }

        // حذف المشروع من قاعدة البيانات
        $deleteQuery = "DELETE FROM projects WHERE id = :id";
        $deleteStmt = $db->prepare($deleteQuery);
        $deleteStmt->bindParam(':id', $id);

        if ($deleteStmt->execute()) {
            // حذف الصورة إذا كانت موجودة
            if ($project['image_url'] && file_exists('../' . $project['image_url'])) {
                unlink('../' . $project['image_url']);
            }

            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المشروع بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('فشل في حذف المشروع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $exception->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
