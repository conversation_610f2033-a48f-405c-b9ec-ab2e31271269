# دليل نظام رفع إثبات الدفع

## 🎯 المشكلة التي تم حلها
- لا يوجد مجلد لحفظ إثبات الدفع
- لا يتم توليد أسماء عشوائية للصور المرفوعة
- لا يتم حفظ مسار الصورة في جدول donations

## ✅ الحلول المطبقة

### 1. إنشاء مجلد الرفع
```
uploads/
├── .htaccess (حماية المجلد)
├── index.php (منع الوصول المباشر)
└── payment_proofs/
    └── index.php (منع الوصول المباشر)
```

### 2. تعديل API (donations.php)

#### دالة توليد اسم عشوائي:
```php
function generateRandomFileName($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $randomName = 'payment_' . uniqid() . '_' . time() . '.' . $extension;
    return $randomName;
}
```

#### دالة رفع الملف:
```php
function uploadPaymentProof($file) {
    $uploadDir = '../uploads/payment_proofs/';
    
    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
    
    // التحقق من حجم الملف (5MB كحد أقصى)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
    }
    
    // توليد اسم عشوائي ورفع الملف
    $fileName = generateRandomFileName($file['name']);
    $filePath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return 'uploads/payment_proofs/' . $fileName;
    }
}
```

### 3. تعديل DonationModal.tsx

#### استخدام FormData بدلاً من JSON:
```typescript
const formDataToSend = new FormData();
formDataToSend.append('project_id', project.id.toString());
formDataToSend.append('donor_name', formData.isAnonymous ? 'متبرع مجهول' : formData.fullName);
// ... باقي البيانات

// إضافة ملف إثبات الدفع
if (formData.paymentProof) {
    formDataToSend.append('payment_proof', formData.paymentProof);
}

// إرسال بدون Content-Type header
const response = await fetch('http://localhost/gaza/api/donations.php', {
    method: 'POST',
    body: formDataToSend
});
```

### 4. تحسين AdminDonations.tsx

#### عرض إثبات الدفع:
```typescript
{selectedDonation.payment_proof && (
  <div>
    <label className="block text-sm font-medium text-gray-700">إثبات الدفع</label>
    <div className="mt-2">
      {selectedDonation.payment_proof.toLowerCase().endsWith('.pdf') ? (
        // عرض أيقونة PDF مع رابط
        <div className="flex items-center gap-2">
          <div className="bg-red-100 p-2 rounded">
            <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              {/* أيقونة PDF */}
            </svg>
          </div>
          <a href={`http://localhost/gaza/${selectedDonation.payment_proof}`} target="_blank">
            عرض الملف
          </a>
        </div>
      ) : (
        // عرض الصورة مع رابط للحجم الكامل
        <div className="relative">
          <img 
            src={`http://localhost/gaza/${selectedDonation.payment_proof}`}
            alt="إثبات الدفع"
            className="max-w-full h-auto max-h-64 rounded-lg border"
          />
          <a href={`http://localhost/gaza/${selectedDonation.payment_proof}`} target="_blank">
            عرض بالحجم الكامل
          </a>
        </div>
      )}
    </div>
  </div>
)}
```

## 🔒 الحماية والأمان

### 1. ملف .htaccess في uploads:
```apache
# منع عرض محتويات المجلد
Options -Indexes

# السماح بعرض الصور وملفات PDF فقط
<FilesMatch "\.(jpg|jpeg|png|gif|pdf)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع تنفيذ ملفات PHP
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# تحديد حد أقصى لحجم الملف (5MB)
LimitRequestBody 5242880
```

### 2. التحقق من نوع الملف:
- صور: JPG, JPEG, PNG, GIF
- مستندات: PDF
- حد أقصى: 5MB

### 3. أسماء ملفات عشوائية:
```
payment_[uniqid]_[timestamp].[extension]
مثال: payment_64a1b2c3d4e5f_1672531200.jpg
```

## 🧪 الاختبار

### 1. اختبار رفع الملفات:
```
http://localhost/gaza/test_upload.php
```

### 2. اختبار API مباشرة:
```bash
curl -X POST \
  -F "project_id=1" \
  -F "donor_name=متبرع تجريبي" \
  -F "donor_email=<EMAIL>" \
  -F "donor_phone=01234567890" \
  -F "donor_country=مصر" \
  -F "amount=100" \
  -F "currency=EGP" \
  -F "message=رسالة تجريبية" \
  -F "is_anonymous=0" \
  -F "status=pending" \
  -F "payment_proof=@/path/to/image.jpg" \
  http://localhost/gaza/api/donations.php
```

### 3. اختبار الواجهة:
1. افتح الموقع: `http://localhost:5173`
2. اختر مشروع واضغط "تبرع الآن"
3. املأ النموذج وارفع صورة إثبات الدفع
4. أرسل التبرع
5. تحقق من لوحة الإدارة: `http://localhost:5173/admin/donations`

## 📁 هيكل الملفات الجديد

```
gaza/
├── uploads/
│   ├── .htaccess
│   ├── index.php
│   └── payment_proofs/
│       ├── index.php
│       └── payment_[id]_[timestamp].[ext] (الملفات المرفوعة)
├── api/
│   └── donations.php (محدث)
├── src/components/
│   ├── DonationModal.tsx (محدث)
│   └── admin/AdminDonations.tsx (محدث)
├── test_upload.php (جديد)
└── UPLOAD_SYSTEM_GUIDE.md (هذا الملف)
```

## 🚀 خطوات التشغيل

1. **تأكد من إعدادات PHP:**
   ```php
   file_uploads = On
   upload_max_filesize = 10M
   post_max_size = 10M
   max_file_uploads = 20
   ```

2. **تأكد من صلاحيات المجلدات:**
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/payment_proofs/
   ```

3. **اختبر النظام:**
   - افتح `http://localhost/gaza/test_upload.php`
   - جرب رفع صورة أو ملف PDF
   - تحقق من حفظ الملف في المجلد

4. **اختبر الواجهة:**
   - افتح الموقع وجرب إرسال تبرع مع إثبات دفع
   - تحقق من عرض الملف في لوحة الإدارة

## ⚠️ ملاحظات مهمة

1. **أمان الملفات:**
   - يتم فحص نوع الملف قبل الرفع
   - يتم توليد أسماء عشوائية لمنع التضارب
   - يتم منع تنفيذ ملفات PHP في مجلد الرفع

2. **حجم الملفات:**
   - الحد الأقصى 5MB لكل ملف
   - يمكن تعديل الحد في API و .htaccess

3. **أنواع الملفات المدعومة:**
   - الصور: JPG, JPEG, PNG, GIF
   - المستندات: PDF

4. **النسخ الاحتياطي:**
   - احرص على عمل نسخة احتياطية من مجلد uploads
   - الملفات المرفوعة لا تُحذف تلقائياً

## 🔧 استكشاف الأخطاء

### مشكلة: لا يتم رفع الملف
- تحقق من صلاحيات المجلد
- تحقق من إعدادات PHP
- تحقق من حجم الملف

### مشكلة: لا يظهر الملف في لوحة الإدارة
- تحقق من مسار الملف في قاعدة البيانات
- تحقق من وجود الملف فعلياً في المجلد

### مشكلة: خطأ في عرض الصورة
- تحقق من مسار الصورة
- تحقق من صلاحيات الوصول للملف
