<?php
// اختبار رفع صور المشاريع
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع صور المشاريع</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; max-width: 400px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #218838; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .preview { max-width: 300px; max-height: 200px; object-fit: cover; border-radius: 8px; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>اختبار رفع صور المشاريع</h1>
    
    <form action="api/projects.php" method="POST" enctype="multipart/form-data">
        <div class="form-group">
            <label>عنوان المشروع:</label>
            <input type="text" name="title" value="مشروع تجريبي" required>
        </div>
        
        <div class="form-group">
            <label>الوصف:</label>
            <textarea name="description" rows="3" required>وصف تجريبي للمشروع</textarea>
        </div>
        
        <div class="form-group">
            <label>الفئة:</label>
            <select name="category" required>
                <option value="طبي">طبي</option>
                <option value="غذائي">غذائي</option>
                <option value="تعليمي">تعليمي</option>
                <option value="إسكان">إسكان</option>
                <option value="مياه">مياه</option>
                <option value="ملابس">ملابس</option>
                <option value="كهرباء">كهرباء</option>
                <option value="أخرى">أخرى</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>المبلغ المستهدف:</label>
            <input type="number" name="target_amount" value="10000" step="0.01" required>
        </div>
        
        <div class="form-group">
            <label>صورة المشروع:</label>
            <input type="file" name="project_image" accept="image/*" onchange="previewImage(this)">
            <small>يُسمح بالصور فقط (JPG, PNG, GIF, WebP) - حد أقصى 10MB</small>
            <div id="imagePreview"></div>
        </div>
        
        <button type="submit">إضافة المشروع</button>
    </form>
    
    <script>
    function previewImage(input) {
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '';
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'preview';
                img.alt = 'معاينة الصورة';
                preview.appendChild(img);
            };
            reader.readAsDataURL(input.files[0]);
        }
    }
    </script>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo '<div class="result">';
        echo '<h3>نتيجة الاختبار:</h3>';
        
        // عرض البيانات المرسلة
        echo '<h4>البيانات المرسلة:</h4>';
        echo '<pre>' . print_r($_POST, true) . '</pre>';
        
        // عرض معلومات الملف
        if (isset($_FILES['project_image'])) {
            echo '<h4>معلومات الصورة:</h4>';
            echo '<pre>' . print_r($_FILES['project_image'], true) . '</pre>';
        }
        
        echo '</div>';
    }
    ?>
    
    <hr>
    
    <h2>اختبار مجلد صور المشاريع</h2>
    <?php
    $uploadDir = 'uploads/project_images/';
    if (is_dir($uploadDir)) {
        echo '<p style="color: green;">✅ مجلد صور المشاريع موجود: ' . $uploadDir . '</p>';
        
        if (is_writable($uploadDir)) {
            echo '<p style="color: green;">✅ مجلد صور المشاريع قابل للكتابة</p>';
        } else {
            echo '<p style="color: red;">❌ مجلد صور المشاريع غير قابل للكتابة</p>';
        }
        
        // عرض الصور الموجودة
        $files = scandir($uploadDir);
        $files = array_filter($files, function($file) {
            return !in_array($file, ['.', '..', 'index.php', '.htaccess']);
        });
        
        if (!empty($files)) {
            echo '<h4>الصور الموجودة:</h4>';
            echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">';
            foreach ($files as $file) {
                $filePath = $uploadDir . $file;
                $fileSize = filesize($filePath);
                $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
                echo '<div style="border: 1px solid #ddd; padding: 10px; border-radius: 8px;">';
                echo '<img src="' . $filePath . '" alt="' . $file . '" style="width: 100%; height: 120px; object-fit: cover; border-radius: 4px;">';
                echo '<p style="margin: 5px 0; font-size: 12px;"><strong>' . $file . '</strong></p>';
                echo '<p style="margin: 0; font-size: 11px; color: #666;">';
                echo number_format($fileSize / 1024, 2) . ' KB - ' . $fileTime;
                echo '</p>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p>لا توجد صور مرفوعة</p>';
        }
    } else {
        echo '<p style="color: red;">❌ مجلد صور المشاريع غير موجود: ' . $uploadDir . '</p>';
    }
    ?>
    
    <h2>المشاريع الموجودة</h2>
    <?php
    try {
        require_once 'api/config/database.php';
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "SELECT id, title, image_url, created_at FROM projects ORDER BY created_at DESC LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($projects)) {
            echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px;">';
            foreach ($projects as $project) {
                echo '<div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px;">';
                echo '<h4>' . htmlspecialchars($project['title']) . '</h4>';
                
                if ($project['image_url']) {
                    if (file_exists($project['image_url'])) {
                        echo '<img src="' . $project['image_url'] . '" alt="صورة المشروع" style="width: 100%; height: 150px; object-fit: cover; border-radius: 4px; margin: 10px 0;">';
                        echo '<p style="color: green; font-size: 12px;">✅ الصورة موجودة</p>';
                    } else {
                        echo '<div style="width: 100%; height: 150px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 4px; margin: 10px 0;">';
                        echo '<span style="color: #666;">صورة غير موجودة</span>';
                        echo '</div>';
                        echo '<p style="color: red; font-size: 12px;">❌ الصورة غير موجودة: ' . $project['image_url'] . '</p>';
                    }
                } else {
                    echo '<div style="width: 100%; height: 150px; background: #f9f9f9; display: flex; align-items: center; justify-content: center; border-radius: 4px; margin: 10px 0;">';
                    echo '<span style="color: #999;">لا توجد صورة</span>';
                    echo '</div>';
                }
                
                echo '<p style="font-size: 12px; color: #666; margin: 5px 0;">المعرف: ' . $project['id'] . '</p>';
                echo '<p style="font-size: 12px; color: #666; margin: 0;">تاريخ الإنشاء: ' . $project['created_at'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p>لا توجد مشاريع</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">خطأ في جلب المشاريع: ' . $e->getMessage() . '</p>';
    }
    ?>
    
    <h2>إعدادات PHP للرفع</h2>
    <ul>
        <li><strong>file_uploads:</strong> <?php echo ini_get('file_uploads') ? 'مفعل' : 'معطل'; ?></li>
        <li><strong>upload_max_filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
        <li><strong>post_max_size:</strong> <?php echo ini_get('post_max_size'); ?></li>
        <li><strong>max_file_uploads:</strong> <?php echo ini_get('max_file_uploads'); ?></li>
        <li><strong>memory_limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
    </ul>
</body>
</html>
