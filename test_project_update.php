<?php
// تشخيص مشكلة تحديث المشروع
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-HTTP-Method-Override');

// تفعيل عرض الأخطاء للتشخيص
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "=== تشخيص تحديث المشروع ===\n";

try {
    echo "1. اختبار الاتصال بقاعدة البيانات...\n";
    require_once 'api/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ الاتصال بقاعدة البيانات نجح\n";
    
    // اختبار هيكل الجدول
    echo "2. فحص هيكل جدول المشاريع...\n";
    $columnsQuery = "SHOW COLUMNS FROM projects";
    $stmt = $db->prepare($columnsQuery);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $columnNames = array_column($columns, 'Field');
    echo "الأعمدة الموجودة: " . implode(', ', $columnNames) . "\n";
    
    $hasDonorsCount = in_array('donors_count', $columnNames);
    echo ($hasDonorsCount ? "✅" : "❌") . " عمود donors_count موجود\n";
    
    // اختبار البيانات المرسلة
    echo "3. فحص البيانات المرسلة...\n";
    echo "طريقة الطلب: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "البيانات POST:\n";
    print_r($_POST);
    echo "الملفات FILES:\n";
    print_r($_FILES);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
        echo "4. محاولة تحديث المشروع...\n";
        
        $project_id = $_POST['id'];
        echo "معرف المشروع: " . $project_id . "\n";
        
        // جلب المشروع الحالي
        $selectQuery = "SELECT * FROM projects WHERE id = :id";
        $stmt = $db->prepare($selectQuery);
        $stmt->bindParam(':id', $project_id);
        $stmt->execute();
        $currentProject = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($currentProject) {
            echo "✅ المشروع موجود: " . $currentProject['title'] . "\n";
            
            // محاولة التحديث
            $updateQuery = "UPDATE projects SET 
                            title = :title,
                            description = :description,
                            category = :category,
                            target_amount = :target_amount,
                            raised_amount = :raised_amount,
                            donors_count = :donors_count,
                            status = :status,
                            updated_at = CURRENT_TIMESTAMP
                            WHERE id = :id";
            
            $stmt = $db->prepare($updateQuery);
            $stmt->bindParam(':id', $project_id);
            $stmt->bindParam(':title', $_POST['title']);
            $stmt->bindParam(':description', $_POST['description']);
            $stmt->bindParam(':category', $_POST['category']);
            $stmt->bindParam(':target_amount', $_POST['target_amount']);
            $stmt->bindParam(':raised_amount', $_POST['raised_amount']);
            $stmt->bindParam(':donors_count', $_POST['donors_count']);
            $stmt->bindParam(':status', $_POST['status']);
            
            if ($stmt->execute()) {
                echo "✅ تم تحديث المشروع بنجاح\n";
                echo "عدد الصفوف المتأثرة: " . $stmt->rowCount() . "\n";
            } else {
                echo "❌ فشل في تحديث المشروع\n";
                $errorInfo = $stmt->errorInfo();
                echo "معلومات الخطأ: " . print_r($errorInfo, true) . "\n";
            }
        } else {
            echo "❌ المشروع غير موجود\n";
        }
    } else {
        echo "4. لم يتم إرسال بيانات تحديث\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
    echo "التتبع:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== انتهى التشخيص ===\n";
?>
