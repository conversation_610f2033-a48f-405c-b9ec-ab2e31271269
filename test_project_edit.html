<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تعديل المشروع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تعديل المشروع</h1>
        
        <form id="editForm">
            <div class="form-group">
                <label for="projectId">معرف المشروع:</label>
                <input type="number" id="projectId" name="id" required>
            </div>
            
            <div class="form-group">
                <label for="title">اسم المشروع:</label>
                <input type="text" id="title" name="title" required>
            </div>
            
            <div class="form-group">
                <label for="description">وصف المشروع:</label>
                <textarea id="description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label for="category">الفئة:</label>
                <select id="category" name="category" required>
                    <option value="">اختر الفئة</option>
                    <option value="طبي">طبي</option>
                    <option value="غذائي">غذائي</option>
                    <option value="تعليمي">تعليمي</option>
                    <option value="إسكان">إسكان</option>
                    <option value="مياه">مياه</option>
                    <option value="ملابس">ملابس</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="targetAmount">المبلغ المستهدف:</label>
                <input type="number" id="targetAmount" name="target_amount" required>
            </div>
            
            <div class="form-group">
                <label for="raisedAmount">المبلغ المُجمع:</label>
                <input type="number" id="raisedAmount" name="raised_amount">
            </div>
            
            <div class="form-group">
                <label for="status">الحالة:</label>
                <select id="status" name="status">
                    <option value="نشط">نشط</option>
                    <option value="مكتمل">مكتمل</option>
                    <option value="متوقف">متوقف</option>
                    <option value="عاجل">عاجل</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="projectImage">صورة المشروع (اختياري):</label>
                <input type="file" id="projectImage" name="project_image" accept="image/*">
            </div>
            
            <button type="submit">تحديث المشروع</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');

            // طباعة البيانات المرسلة
            console.log('Form data entries:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }

            try {
                console.log('Sending form data...');

                const response = await fetch('http://localhost/gaza/api/projects.php', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = '<div class="result success">تم تحديث المشروع بنجاح!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">خطأ: ' + data.message + '</div>';
                }
            } catch (error) {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = '<div class="result error">خطأ في الاتصال: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
