<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بـ API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار الاتصال بـ API</h1>
        
        <button onclick="testConnection()">اختبار الاتصال الأساسي</button>
        <button onclick="testGetProjects()">اختبار جلب المشاريع</button>
        <button onclick="testGetSpecificProject()">اختبار جلب مشروع محدد</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost/gaza/api/projects.php';
        const resultDiv = document.getElementById('result');

        async function testConnection() {
            resultDiv.innerHTML = '<div class="result info">جاري اختبار الاتصال...</div>';
            
            try {
                const response = await fetch(API_URL);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="result success">
نجح الاتصال!
الحالة: ${response.status}
البيانات: ${JSON.stringify(data, null, 2)}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
فشل الاتصال!
الخطأ: ${error.message}
                </div>`;
            }
        }

        async function testGetProjects() {
            resultDiv.innerHTML = '<div class="result info">جاري جلب المشاريع...</div>';
            
            try {
                const response = await fetch(API_URL + '?page=1&limit=5');
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="result success">
نجح جلب المشاريع!
الحالة: ${response.status}
عدد المشاريع: ${data.projects ? data.projects.length : 'غير محدد'}
البيانات: ${JSON.stringify(data, null, 2)}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
فشل جلب المشاريع!
الخطأ: ${error.message}
                </div>`;
            }
        }

        async function testGetSpecificProject() {
            const projectId = prompt('أدخل معرف المشروع للاختبار:', '1');
            if (!projectId) return;
            
            resultDiv.innerHTML = '<div class="result info">جاري جلب المشروع...</div>';
            
            try {
                const response = await fetch(API_URL + '?id=' + projectId);
                const data = await response.json();
                
                resultDiv.innerHTML = `<div class="result success">
نجح جلب المشروع!
الحالة: ${response.status}
البيانات: ${JSON.stringify(data, null, 2)}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">
فشل جلب المشروع!
الخطأ: ${error.message}
                </div>`;
            }
        }
    </script>
</body>
</html>
