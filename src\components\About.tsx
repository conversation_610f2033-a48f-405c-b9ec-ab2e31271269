
import React from 'react';
import { Heart, Users, Target, Award, Shield, Globe } from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "الإنسانية",
      description: "نؤمن بقيمة الإنسان وحقه في الكرامة والعيش بسلام"
    },
    {
      icon: Shield,
      title: "الشفافية",
      description: "نتميز بالشفافية الكاملة في استخدام التبرعات والتقارير المالية"
    },
    {
      icon: Users,
      title: "التضامن",
      description: "نبني جسور التضامن بين الشعوب لدعم المحتاجين"
    },
    {
      icon: Target,
      title: "الفعالية",
      description: "نحرص على الوصول المباشر للمستفيدين بأقصى فعالية ممكنة"
    }
  ];

  const achievements = [
    { icon: Users, number: "50,000+", label: "مستفيد مباشر" },
    { icon: Globe, number: "25", label: "دولة متبرعة" },
    { icon: Award, number: "200+", label: "مشروع مكتمل" },
    { icon: Heart, number: "5", label: "سنوات خبرة" }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gaza-green font-amiri">
            من نحن
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            منظمة إنسانية تعمل على تقديم المساعدات العاجلة والتنموية لأهل غزة من خلال شبكة واسعة من المتبرعين والشركاء
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Story Content */}
          <div className="space-y-6 animate-slide-in-right">
            <h3 className="text-3xl font-bold text-gaza-green font-amiri mb-6">
              قصتنا
            </h3>
            <p className="text-lg leading-relaxed text-muted-foreground">
              انطلقت رؤيتنا من إيماننا العميق بأن كل إنسان يستحق حياة كريمة. بدأنا رحلتنا في عام 2019 
              كمجموعة من المتطوعين الذين آمنوا بضرورة تقديم الدعم لأهل غزة في ظل الظروف الصعبة التي يواجهونها.
            </p>
            <p className="text-lg leading-relaxed text-muted-foreground">
              على مدار السنوات الماضية، نجحنا في بناء جسور الثقة مع المتبرعين والمستفيدين على حد سواء، 
              وأصبحنا اليوم واحدة من المنظمات الرائدة في مجال العمل الإنساني لدعم قطاع غزة.
            </p>
            <p className="text-lg leading-relaxed text-muted-foreground">
              نعمل بشفافية كاملة ونقدم تقارير دورية لجميع متبرعينا لضمان وصول المساعدات 
              لمستحقيها بأفضل الطرق وأكثرها فعالية.
            </p>
          </div>

          {/* Visual Element */}
          <div className="relative animate-fade-in">
            <div className="bg-gradient-to-br from-gaza-green to-gaza-gold rounded-2xl p-8 text-white">
              <div className="grid grid-cols-2 gap-6">
                {achievements.map((achievement, index) => (
                  <div
                    key={index}
                    className="text-center p-4 bg-white/10 backdrop-blur-sm rounded-xl"
                  >
                    <achievement.icon className="h-8 w-8 mx-auto mb-3 text-gaza-gold" />
                    <div className="text-2xl font-bold mb-1">{achievement.number}</div>
                    <div className="text-sm opacity-90">{achievement.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gaza-green font-amiri mb-4">
              قيمنا ومبادئنا
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              نسترشد بمجموعة من القيم والمبادئ الراسخة التي توجه عملنا وتضمن تحقيق أهدافنا الإنسانية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="text-center p-6 bg-gray-50 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300 card-hover animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-gaza-green p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <value.icon className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gaza-green mb-3 font-amiri">
                  {value.title}
                </h4>
                <p className="text-muted-foreground leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-gradient-to-br from-gaza-green to-gaza-green/80 text-white p-8 rounded-2xl animate-fade-in">
            <Target className="h-12 w-12 mb-6 text-gaza-gold" />
            <h3 className="text-2xl font-bold mb-4 font-amiri">رسالتنا</h3>
            <p className="leading-relaxed">
              تقديم المساعدات الإنسانية العاجلة والتنموية لأهل غزة بشفافية وفعالية، 
              وبناء جسور التضامن بين الشعوب لدعم القضايا الإنسانية العادلة.
            </p>
          </div>

          <div className="bg-gradient-to-br from-gaza-gold to-gaza-gold/80 text-white p-8 rounded-2xl animate-fade-in">
            <Globe className="h-12 w-12 mb-6 text-gaza-green" />
            <h3 className="text-2xl font-bold mb-4 font-amiri">رؤيتنا</h3>
            <p className="leading-relaxed">
              أن نكون المنظمة الرائدة في تقديم الدعم الإنساني لأهل غزة، 
              ونساهم في بناء مجتمع عالمي متضامن يقف مع قضايا العدالة والكرامة الإنسانية.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
