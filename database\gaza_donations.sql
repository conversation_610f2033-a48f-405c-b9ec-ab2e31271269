-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS gaza_donations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gaza_donations;

-- جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT 'عنوان المشروع',
    description TEXT COMMENT 'وصف المشروع',
    category VARCHAR(100) NOT NULL COMMENT 'فئة المشروع',
    target_amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'المبلغ المستهدف',
    raised_amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'المبلغ المجمع',
    status ENUM('نشط', 'متوقف', 'مكتمل') DEFAULT 'نشط' COMMENT 'حالة المشروع',
    image_url VARCHAR(500) COMMENT 'رابط صورة المشروع',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التبرعات
CREATE TABLE IF NOT EXISTS donations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL COMMENT 'معرف المشروع',
    donor_name VARCHAR(255) COMMENT 'اسم المتبرع',
    donor_email VARCHAR(255) COMMENT 'بريد المتبرع الإلكتروني',
    donor_phone VARCHAR(50) COMMENT 'رقم هاتف المتبرع',
    donor_country VARCHAR(100) COMMENT 'دولة المتبرع',
    amount DECIMAL(10,2) NOT NULL COMMENT 'مبلغ التبرع',
    currency ENUM('EGP', 'USD', 'EUR') DEFAULT 'EGP' COMMENT 'عملة التبرع',
    amount_in_egp DECIMAL(10,2) NOT NULL COMMENT 'المبلغ بالجنيه المصري',
    message TEXT COMMENT 'رسالة المتبرع',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT 'تبرع مجهول',
    payment_proof VARCHAR(255) COMMENT 'إثبات الدفع',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT 'حالة التبرع',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ التبرع',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية للمشاريع
INSERT INTO projects (title, description, category, target_amount, raised_amount, status, image_url) VALUES
('مساعدات طبية عاجلة', 'توفير الأدوية والمعدات الطبية للمحتاجين في غزة', 'طبي', 100000.00, 65000.00, 'نشط', '/images/medical-aid.jpg'),
('مساعدات غذائية للأسر', 'توزيع الطعام والمواد الغذائية على الأسر المحتاجة', 'غذائي', 75000.00, 45000.00, 'نشط', '/images/food-aid.jpg'),
('تعليم الأطفال', 'دعم التعليم وتوفير الأدوات المدرسية للأطفال', 'تعليمي', 50000.00, 30000.00, 'نشط', '/images/education.jpg'),
('مشروع الإسكان الطارئ', 'توفير مأوى مؤقت للعائلات المتضررة', 'إسكان', 200000.00, 85000.00, 'متوقف', '/images/housing.jpg'),
('مساعدات شتوية', 'توفير الملابس الشتوية والبطانيات', 'ملابس', 30000.00, 28000.00, 'مكتمل', '/images/winter-aid.jpg');

-- إدراج بيانات تجريبية للتبرعات
INSERT INTO donations (project_id, donor_name, donor_email, amount, message, is_anonymous) VALUES
(1, 'أحمد محمد', '<EMAIL>', 500.00, 'بارك الله فيكم على هذا العمل الخيري', FALSE),
(1, 'فاطمة علي', '<EMAIL>', 1000.00, 'نسأل الله أن يتقبل منا ومنكم', FALSE),
(2, NULL, NULL, 750.00, 'تبرع مجهول', TRUE),
(2, 'محمد حسن', '<EMAIL>', 300.00, 'جعله الله في ميزان حسناتكم', FALSE),
(3, 'عائشة أحمد', '<EMAIL>', 200.00, 'للأطفال الأعزاء', FALSE),
(4, 'يوسف إبراهيم', '<EMAIL>', 2000.00, 'نسأل الله أن يفرج عن إخواننا', FALSE);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_category ON projects(category);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_donations_project_id ON donations(project_id);
CREATE INDEX idx_donations_created_at ON donations(created_at);

-- إنشاء trigger لتحديث المبلغ المجمع تلقائياً عند إضافة تبرع جديد
DELIMITER //
CREATE TRIGGER update_raised_amount_after_donation
AFTER INSERT ON donations
FOR EACH ROW
BEGIN
    UPDATE projects 
    SET raised_amount = (
        SELECT COALESCE(SUM(amount), 0) 
        FROM donations 
        WHERE project_id = NEW.project_id
    )
    WHERE id = NEW.project_id;
    
    -- تحديث حالة المشروع إلى مكتمل إذا تم الوصول للهدف
    UPDATE projects 
    SET status = 'مكتمل'
    WHERE id = NEW.project_id 
    AND raised_amount >= target_amount 
    AND status = 'نشط';
END//
DELIMITER ;

-- إنشاء trigger لتحديث المبلغ المجمع عند حذف تبرع
DELIMITER //
CREATE TRIGGER update_raised_amount_after_delete_donation
AFTER DELETE ON donations
FOR EACH ROW
BEGIN
    UPDATE projects 
    SET raised_amount = (
        SELECT COALESCE(SUM(amount), 0) 
        FROM donations 
        WHERE project_id = OLD.project_id
    )
    WHERE id = OLD.project_id;
END//
DELIMITER ;

-- إدراج بيانات تجريبية للتبرعات
INSERT INTO donations (project_id, donor_name, donor_email, donor_phone, donor_country, amount, currency, amount_in_egp, message, is_anonymous, status) VALUES
(1, 'أحمد محمد', '<EMAIL>', '+201234567890', 'مصر', 500.00, 'EGP', 500.00, 'بارك الله فيكم على هذا العمل الخيري', FALSE, 'approved'),
(1, 'فاطمة علي', '<EMAIL>', '+966501234567', 'السعودية', 100.00, 'USD', 4950.00, 'نسأل الله أن يتقبل منا ومنكم', FALSE, 'approved'),
(2, 'متبرع مجهول', '<EMAIL>', '+971501234567', 'الإمارات', 50.00, 'EUR', 2660.00, '', TRUE, 'approved'),
(2, 'محمد حسن', '<EMAIL>', '+962791234567', 'الأردن', 200.00, 'EGP', 200.00, 'اللهم بارك في هذا المال', FALSE, 'pending'),
(3, 'سارة أحمد', '<EMAIL>', '+96171234567', 'لبنان', 75.00, 'USD', 3712.50, 'للأطفال الأبرياء في غزة', FALSE, 'approved'),
(1, 'عبدالله خالد', '<EMAIL>', '+96891234567', 'عمان', 25.00, 'EUR', 1330.00, 'نصرة لإخواننا في فلسطين', FALSE, 'approved'),
(3, 'ليلى محمود', '<EMAIL>', '+97431234567', 'قطر', 150.00, 'USD', 7425.00, 'من أجل تعليم أطفال غزة', FALSE, 'approved'),
(4, 'متبرع مجهول', '<EMAIL>', '+97331234567', 'البحرين', 300.00, 'EGP', 300.00, '', TRUE, 'rejected');
