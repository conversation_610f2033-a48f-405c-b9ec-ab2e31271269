
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, FolderPlus, BarChart3, Users, Settings, Heart, DollarSign } from 'lucide-react';

const AdminSidebar = () => {
  const location = useLocation();

  const menuItems = [
    { name: 'الرئيسية', path: '/admin', icon: Home },
    { name: 'المشاريع', path: '/admin/projects', icon: FolderPlus },
    { name: 'التبرعات', path: '/admin/donations', icon: DollarSign },
    { name: 'الإحصائيات', path: '/admin/stats', icon: BarChart3 },
  ];

  return (
    <div className="w-64 bg-white shadow-lg border-l border-gray-200">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="bg-gradient-to-r from-gaza-green to-gaza-gold p-2 rounded-lg">
            <Heart className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gaza-green font-amiri">لوحة الإدارة</h2>
            <p className="text-xs text-muted-foreground">دعم غزة</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <li key={item.name}>
                <Link
                  to={item.path}
                  className={`flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? 'bg-gaza-green text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Back to Site */}
      <div className="absolute bottom-4 left-4 right-4">
        <Link
          to="/"
          className="block w-full text-center px-4 py-2 text-gaza-green border border-gaza-green rounded-lg hover:bg-gaza-green hover:text-white transition-colors duration-200"
        >
          العودة للموقع
        </Link>
      </div>
    </div>
  );
};

export default AdminSidebar;
