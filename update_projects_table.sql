-- إضافة حقل عدد المتبرعين إلى جدول المشاريع
ALTER TABLE projects ADD COLUMN donors_count INT DEFAULT 0 AFTER raised_amount;

-- تحديث عدد المتبرعين للمشاريع الموجودة
UPDATE projects p 
SET donors_count = (
    SELECT COUNT(DISTINCT d.id) 
    FROM donations d 
    WHERE d.project_id = p.id 
    AND d.status = 'مؤكد'
);

-- إنشاء trigger لتحديث عدد المتبرعين تلقائياً عند إضافة تبرع جديد
DELIMITER //

DROP TRIGGER IF EXISTS update_project_donors_count_after_insert//
CREATE TRIGGER update_project_donors_count_after_insert
AFTER INSERT ON donations
FOR EACH ROW
BEGIN
    UPDATE projects 
    SET donors_count = (
        SELECT COUNT(DISTINCT d.id) 
        FROM donations d 
        WHERE d.project_id = NEW.project_id 
        AND d.status = 'مؤكد'
    )
    WHERE id = NEW.project_id;
END//

DROP TRIGGER IF EXISTS update_project_donors_count_after_update//
CREATE TRIGGER update_project_donors_count_after_update
AFTER UPDATE ON donations
FOR EACH ROW
BEGIN
    -- تحديث المشروع القديم إذا تغير project_id
    IF OLD.project_id != NEW.project_id THEN
        UPDATE projects 
        SET donors_count = (
            SELECT COUNT(DISTINCT d.id) 
            FROM donations d 
            WHERE d.project_id = OLD.project_id 
            AND d.status = 'مؤكد'
        )
        WHERE id = OLD.project_id;
    END IF;
    
    -- تحديث المشروع الجديد
    UPDATE projects 
    SET donors_count = (
        SELECT COUNT(DISTINCT d.id) 
        FROM donations d 
        WHERE d.project_id = NEW.project_id 
        AND d.status = 'مؤكد'
    )
    WHERE id = NEW.project_id;
END//

DROP TRIGGER IF EXISTS update_project_donors_count_after_delete//
CREATE TRIGGER update_project_donors_count_after_delete
AFTER DELETE ON donations
FOR EACH ROW
BEGIN
    UPDATE projects 
    SET donors_count = (
        SELECT COUNT(DISTINCT d.id) 
        FROM donations d 
        WHERE d.project_id = OLD.project_id 
        AND d.status = 'مؤكد'
    )
    WHERE id = OLD.project_id;
END//

DELIMITER ;

-- عرض هيكل الجدول المحدث
DESCRIBE projects;
