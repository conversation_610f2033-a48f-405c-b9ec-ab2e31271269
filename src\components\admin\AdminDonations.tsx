import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Check, X, DollarSign, Users, TrendingUp, Calendar } from 'lucide-react';

interface Donation {
  id: number;
  project_id: number;
  project_title: string;
  donor_name: string;
  donor_email: string;
  donor_phone: string;
  donor_country: string;
  amount: number;
  currency: string;
  amount_in_egp: number;
  message: string;
  is_anonymous: boolean;
  payment_proof: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
}

interface DonationStats {
  total_donations: number;
  total_amount_egp: number;
  projects_with_donations: number;
  average_donation: number;
}

const AdminDonations = () => {
  const [donations, setDonations] = useState<Donation[]>([]);
  const [stats, setStats] = useState<DonationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedDonation, setSelectedDonation] = useState<Donation | null>(null);

  // جلب التبرعات من API
  const fetchDonations = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost/gaza/api/donations.php');
      const data = await response.json();

      if (data.success) {
        // التأكد من أن البيانات صحيحة
        const donationsArray = Array.isArray(data.data) ? data.data : [];
        const validatedData = donationsArray.map(donation => ({
          ...donation,
          donor_name: donation.donor_name || '',
          donor_email: donation.donor_email || '',
          donor_phone: donation.donor_phone || '',
          donor_country: donation.donor_country || '',
          project_title: donation.project_title || 'مشروع غير محدد',
          message: donation.message || '',
          currency: donation.currency || 'EGP',
          status: donation.status || 'pending'
        }));
        setDonations(validatedData);
      } else {
        console.error('Error fetching donations:', data.message);
        setDonations([]);
      }
    } catch (error) {
      console.error('Error fetching donations:', error);
    } finally {
      setLoading(false);
    }
  };

  // تحديث حالة التبرع
  const updateDonationStatus = async (donationId: number, status: string) => {
    try {
      const response = await fetch('http://localhost/gaza/api/donations.php', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: donationId,
          status: status
        })
      });

      const result = await response.json();
      
      if (result.success) {
        // تحديث القائمة محلياً
        setDonations(prev => 
          prev.map(donation => 
            donation.id === donationId 
              ? { ...donation, status: status as any }
              : donation
          )
        );
        
        alert('تم تحديث حالة التبرع بنجاح');
      } else {
        alert('خطأ في تحديث حالة التبرع');
      }
    } catch (error) {
      console.error('Error updating donation:', error);
      alert('خطأ في تحديث حالة التبرع');
    }
  };

  useEffect(() => {
    fetchDonations();
  }, []);

  // فلترة التبرعات
  const filteredDonations = donations.filter(donation => {
    const donorName = donation.donor_name || '';
    const projectTitle = donation.project_title || '';
    const donorEmail = donation.donor_email || '';

    const matchesSearch = donorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         projectTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         donorEmail.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || donation.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // حساب الإحصائيات
  const calculateStats = () => {
    const approvedDonations = donations.filter(d => d.status === 'approved');
    return {
      total_donations: donations.length,
      total_amount_egp: approvedDonations.reduce((sum, d) => sum + Number(d.amount_in_egp), 0),
      projects_with_donations: new Set(approvedDonations.map(d => d.project_id)).size,
      average_donation: approvedDonations.length > 0 
        ? approvedDonations.reduce((sum, d) => sum + Number(d.amount_in_egp), 0) / approvedDonations.length 
        : 0
    };
  };

  const currentStats = calculateStats();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'مقبول';
      case 'rejected': return 'مرفوض';
      case 'pending': return 'في الانتظار';
      default: return status;
    }
  };

  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case 'USD': return '$';
      case 'EUR': return '€';
      case 'EGP': return 'ج.م';
      default: return currency;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gaza-green"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات التبرعات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي التبرعات</p>
              <p className="text-2xl font-bold text-gaza-green">{currentStats.total_donations}</p>
            </div>
            <Users className="h-8 w-8 text-gaza-green" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">المبلغ الإجمالي</p>
              <p className="text-2xl font-bold text-gaza-green">
                {currentStats.total_amount_egp.toLocaleString()} ج.م
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-gaza-green" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">المشاريع المدعومة</p>
              <p className="text-2xl font-bold text-gaza-green">{currentStats.projects_with_donations}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-gaza-green" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">متوسط التبرع</p>
              <p className="text-2xl font-bold text-gaza-green">
                {Math.round(currentStats.average_donation).toLocaleString()} ج.م
              </p>
            </div>
            <Calendar className="h-8 w-8 text-gaza-green" />
          </div>
        </div>
      </div>

      {/* أدوات البحث والفلترة */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث في التبرعات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="approved">مقبول</option>
              <option value="rejected">مرفوض</option>
            </select>
          </div>
        </div>
      </div>

      {/* جدول التبرعات */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المتبرع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المشروع
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredDonations.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <DollarSign className="h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-lg font-medium">لا توجد تبرعات</p>
                      <p className="text-sm">لم يتم العثور على أي تبرعات تطابق البحث</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredDonations.map((donation) => (
                <tr key={donation.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {donation.is_anonymous ? 'متبرع مجهول' : (donation.donor_name || 'غير محدد')}
                      </div>
                      <div className="text-sm text-gray-500">{donation.donor_country || 'غير محدد'}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{donation.project_title || 'مشروع غير محدد'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {Number(donation.amount).toLocaleString()} {getCurrencySymbol(donation.currency)}
                    </div>
                    <div className="text-xs text-gray-500">
                      ({Number(donation.amount_in_egp).toLocaleString()} ج.م)
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(donation.status)}`}>
                      {getStatusText(donation.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(donation.created_at).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button
                        onClick={() => setSelectedDonation(donation)}
                        className="text-gaza-green hover:text-gaza-green/80"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {donation.status === 'pending' && (
                        <>
                          <button
                            onClick={() => updateDonationStatus(donation.id, 'approved')}
                            className="text-green-600 hover:text-green-800"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => updateDonationStatus(donation.id, 'rejected')}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* نافذة تفاصيل التبرع */}
      {selectedDonation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">تفاصيل التبرع</h3>
                <button
                  onClick={() => setSelectedDonation(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">اسم المتبرع</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedDonation.is_anonymous ? 'متبرع مجهول' : (selectedDonation.donor_name || 'غير محدد')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedDonation.donor_email || 'غير محدد'}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم الهاتف</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedDonation.donor_phone || 'غير محدد'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الدولة</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedDonation.donor_country || 'غير محدد'}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">المبلغ</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {Number(selectedDonation.amount).toLocaleString()} {getCurrencySymbol(selectedDonation.currency)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">المبلغ بالجنيه المصري</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {Number(selectedDonation.amount_in_egp).toLocaleString()} ج.م
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">المشروع</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedDonation.project_title || 'مشروع غير محدد'}</p>
                </div>

                {selectedDonation.message && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الرسالة</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedDonation.message}</p>
                  </div>
                )}

                {selectedDonation.payment_proof && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">إثبات الدفع</label>
                    <div className="mt-2">
                      {selectedDonation.payment_proof.toLowerCase().endsWith('.pdf') ? (
                        <div className="flex items-center gap-2">
                          <div className="bg-red-100 p-2 rounded">
                            <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">ملف PDF</p>
                            <a
                              href={`http://localhost/gaza/${selectedDonation.payment_proof}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-gaza-green hover:text-gaza-green/80"
                            >
                              عرض الملف
                            </a>
                          </div>
                        </div>
                      ) : (
                        <div className="relative">
                          <img
                            src={`http://localhost/gaza/${selectedDonation.payment_proof}`}
                            alt="إثبات الدفع"
                            className="max-w-full h-auto max-h-64 rounded-lg border border-gray-300"
                            onError={(e) => {
                              e.currentTarget.src = '/placeholder-image.png';
                              e.currentTarget.alt = 'لا يمكن عرض الصورة';
                            }}
                          />
                          <a
                            href={`http://localhost/gaza/${selectedDonation.payment_proof}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded text-xs hover:bg-opacity-70"
                          >
                            عرض بالحجم الكامل
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700">الحالة</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedDonation.status)}`}>
                    {getStatusText(selectedDonation.status)}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">تاريخ التبرع</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedDonation.created_at).toLocaleString('ar-SA')}
                  </p>
                </div>
              </div>

              {selectedDonation.status === 'pending' && (
                <div className="mt-6 flex gap-3">
                  <button
                    onClick={() => {
                      updateDonationStatus(selectedDonation.id, 'approved');
                      setSelectedDonation(null);
                    }}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                  >
                    قبول التبرع
                  </button>
                  <button
                    onClick={() => {
                      updateDonationStatus(selectedDonation.id, 'rejected');
                      setSelectedDonation(null);
                    }}
                    className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
                  >
                    رفض التبرع
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDonations;
