<?php
// اختبار مباشر لملف api/projects.php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار مباشر لملف api/projects.php ===\n";

// تعيين متغيرات البيئة للاختبار
$_SERVER['REQUEST_METHOD'] = 'GET';

ob_start();
$error_occurred = false;

try {
    // محاولة تضمين الملف
    include 'api/projects.php';
    $output = ob_get_contents();
    echo "✅ تم تشغيل الملف بنجاح\n";
    echo "الإخراج:\n" . $output . "\n";
} catch (ParseError $e) {
    $error_occurred = true;
    echo "❌ خطأ في parsing: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
} catch (Exception $e) {
    $error_occurred = true;
    echo "❌ خطأ في التشغيل: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
} catch (Error $e) {
    $error_occurred = true;
    echo "❌ خطأ فادح: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}

ob_end_clean();

if (!$error_occurred) {
    echo "\n=== اختبار POST request ===\n";
    
    // محاكاة POST request
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST = [
        'id' => '12',
        'title' => 'اختبار',
        'description' => 'وصف اختبار',
        'category' => 'مياه',
        'target_amount' => '1000.00',
        'raised_amount' => '500.00',
        'donors_count' => '10',
        'status' => 'نشط'
    ];
    
    ob_start();
    try {
        include 'api/projects.php';
        $output = ob_get_contents();
        echo "✅ تم تشغيل POST request بنجاح\n";
        echo "الإخراج:\n" . $output . "\n";
    } catch (Exception $e) {
        echo "❌ خطأ في POST request: " . $e->getMessage() . "\n";
        echo "الملف: " . $e->getFile() . "\n";
        echo "السطر: " . $e->getLine() . "\n";
    }
    ob_end_clean();
}

echo "\n=== انتهى الاختبار ===\n";
?>
