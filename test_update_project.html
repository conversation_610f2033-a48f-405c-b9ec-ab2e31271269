<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث المشروع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تحديث المشروع</h1>
        
        <form id="updateForm">
            <div class="form-group">
                <label for="project_id">معرف المشروع:</label>
                <input type="number" id="project_id" name="id" value="15" required>
            </div>
            
            <div class="form-group">
                <label for="title">عنوان المشروع:</label>
                <input type="text" id="title" name="title" value="تيست محدث" required>
            </div>
            
            <div class="form-group">
                <label for="description">وصف المشروع:</label>
                <textarea id="description" name="description" rows="3">وصف المشروع المحدث</textarea>
            </div>
            
            <div class="form-group">
                <label for="category">الفئة:</label>
                <select id="category" name="category" required>
                    <option value="طبي">طبي</option>
                    <option value="غذائي">غذائي</option>
                    <option value="تعليمي" selected>تعليمي</option>
                    <option value="إسكان">إسكان</option>
                    <option value="مياه">مياه</option>
                    <option value="ملابس">ملابس</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="أخرى">أخرى</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="target_amount">المبلغ المستهدف:</label>
                <input type="number" id="target_amount" name="target_amount" value="50000" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="raised_amount">المبلغ المجمع:</label>
                <input type="number" id="raised_amount" name="raised_amount" value="25000" step="0.01">
            </div>
            
            <div class="form-group">
                <label for="status">الحالة:</label>
                <select id="status" name="status">
                    <option value="نشط" selected>نشط</option>
                    <option value="متوقف">متوقف</option>
                    <option value="مكتمل">مكتمل</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="project_image">صورة المشروع (اختياري):</label>
                <input type="file" id="project_image" name="project_image" accept="image/*">
            </div>
            
            <button type="button" onclick="testUpdate()">تحديث المشروع</button>
            <button type="button" onclick="clearResult()">مسح النتيجة</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        async function testUpdate() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'جاري إرسال الطلب...';
            resultDiv.className = 'result';
            
            try {
                const form = document.getElementById('updateForm');
                const formData = new FormData(form);
                
                // طباعة البيانات المرسلة
                console.log('البيانات المرسلة:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ':', value);
                }
                
                const response = await fetch('http://localhost/gaza/api/projects.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                // قراءة النص الخام أولاً
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                // محاولة تحويل النص إلى JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('استجابة غير صالحة من الخادم: ' + responseText.substring(0, 200));
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `نجح التحديث!\n\nالرسالة: ${data.message}\n\nالاستجابة الكاملة:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `فشل التحديث!\n\nالرسالة: ${data.message}\n\nالاستجابة الكاملة:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                console.error('خطأ:', error);
                resultDiv.innerHTML = `خطأ في الطلب:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('result').className = '';
        }
    </script>
</body>
</html>
