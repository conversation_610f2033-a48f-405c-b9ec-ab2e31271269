# تعليمات إعداد قاعدة البيانات - موقع التبرعات لغزة

## 1. إعداد قاعدة البيانات

### الخطوة الأولى: إنشاء قاعدة البيانات
1. افتح phpMyAdmin من خلال XAMPP
2. انقر على "New" لإنشاء قاعدة بيانات جديدة
3. اكتب اسم قاعدة البيانات: `gaza_donations`
4. اختر Collation: `utf8mb4_unicode_ci`
5. انقر على "Create"

### الخطوة الثانية: استيراد الجداول
1. انقر على قاعدة البيانات `gaza_donations`
2. انقر على تبويب "Import"
3. انقر على "Choose File" واختر الملف `database/gaza_donations.sql`
4. انقر على "Go" لتنفيذ الاستعلامات

## 2. هيكل قاعدة البيانات

### جدول المشاريع (projects)
```sql
- id: معرف المشروع (Primary Key)
- title: عنوان المشروع
- description: وصف المشروع
- category: فئة المشروع (طبي، غذائي، تعليمي، إلخ)
- target_amount: المبلغ المستهدف
- raised_amount: المبلغ المجمع (يتم تحديثه تلقائياً)
- status: حالة المشروع (نشط، متوقف، مكتمل)
- image_url: رابط صورة المشروع
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

### جدول التبرعات (donations)
```sql
- id: معرف التبرع (Primary Key)
- project_id: معرف المشروع (Foreign Key)
- donor_name: اسم المتبرع
- donor_email: بريد المتبرع الإلكتروني
- amount: مبلغ التبرع
- message: رسالة المتبرع
- is_anonymous: تبرع مجهول (true/false)
- created_at: تاريخ التبرع
```

## 3. API Endpoints

### المشاريع
- `GET /api/projects.php` - جلب جميع المشاريع
- `POST /api/projects.php` - إضافة مشروع جديد
- `PUT /api/projects.php` - تحديث مشروع موجود
- `DELETE /api/projects.php?id={id}` - حذف مشروع

### مثال على إضافة مشروع جديد:
```json
{
  "title": "مساعدات طبية عاجلة",
  "description": "توفير الأدوية والمعدات الطبية للمحتاجين",
  "category": "طبي",
  "target_amount": 100000,
  "image_url": "https://example.com/image.jpg"
}
```

## 4. الميزات المتقدمة

### التحديث التلقائي للمبلغ المجمع
- يتم تحديث `raised_amount` تلقائياً عند إضافة تبرع جديد
- يتم تغيير حالة المشروع إلى "مكتمل" عند الوصول للهدف

### الحماية والأمان
- صفحة الإدارة محمية بكلمة مرور
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `17417723`

## 5. تشغيل النظام

### متطلبات التشغيل
1. XAMPP مع Apache و MySQL
2. Node.js و npm
3. React و TypeScript

### خطوات التشغيل
1. تشغيل XAMPP (Apache + MySQL)
2. إنشاء قاعدة البيانات كما هو موضح أعلاه
3. تشغيل الواجهة الأمامية:
   ```bash
   npm install
   npm run dev
   ```
4. الوصول للموقع: `http://localhost:5173`
5. الوصول للإدارة: `http://localhost:5173/admin`

## 6. استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MySQL في XAMPP
   - تحقق من إعدادات قاعدة البيانات في `api/config/database.php`

2. **CORS Error**
   - تأكد من إعدادات CORS في ملفات PHP
   - تحقق من رابط API في الكود

3. **البيانات لا تظهر**
   - تحقق من وجود البيانات في قاعدة البيانات
   - تحقق من console في المتصفح للأخطاء

## 7. إضافة بيانات تجريبية

تم إضافة بيانات تجريبية تلقائياً عند تشغيل ملف SQL:
- 5 مشاريع تجريبية
- 6 تبرعات تجريبية
- حالات مختلفة للمشاريع (نشط، متوقف، مكتمل)

## 8. التطوير المستقبلي

### ميزات يمكن إضافتها:
- نظام المستخدمين والتسجيل
- نظام الدفع الإلكتروني
- تقارير مفصلة
- إشعارات البريد الإلكتروني
- لوحة تحكم متقدمة للإحصائيات
