<?php
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// إنشاء الجداول إذا لم تكن موجودة
$database->createTables();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        getDonations($db);
        break;
    case 'POST':
        createDonation($db);
        break;
    case 'PUT':
        updateDonation($db);
        break;
    case 'DELETE':
        deleteDonation($db);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}

function getDonations($db) {
    try {
        $project_id = $_GET['project_id'] ?? null;
        
        $query = "SELECT 
            d.id,
            d.project_id,
            d.donor_name,
            d.donor_email,
            d.donor_phone,
            d.donor_country,
            d.amount,
            d.currency,
            d.amount_in_egp,
            d.message,
            d.is_anonymous,
            d.payment_proof,
            d.status,
            d.created_at,
            p.title as project_title
        FROM donations d 
        LEFT JOIN projects p ON d.project_id = p.id";
        
        if ($project_id) {
            $query .= " WHERE d.project_id = :project_id";
        }
        
        $query .= " ORDER BY d.created_at DESC";
        
        $stmt = $db->prepare($query);
        
        if ($project_id) {
            $stmt->bindParam(':project_id', $project_id);
        }
        
        $stmt->execute();
        $donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $donations
        ]);
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب التبرعات: ' . $exception->getMessage()
        ]);
    }
}

// دالة لتوليد اسم ملف عشوائي
function generateRandomFileName($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $randomName = 'payment_' . uniqid() . '_' . time() . '.' . $extension;
    return $randomName;
}

// دالة لرفع ملف إثبات الدفع
function uploadPaymentProof($file) {
    $uploadDir = '../uploads/payment_proofs/';

    // التأكد من وجود المجلد
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('نوع الملف غير مدعوم. يرجى رفع صورة أو ملف PDF');
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5MB');
    }

    // توليد اسم عشوائي للملف
    $fileName = generateRandomFileName($file['name']);
    $filePath = $uploadDir . $fileName;

    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return 'uploads/payment_proofs/' . $fileName;
    } else {
        throw new Exception('فشل في رفع الملف');
    }
}

function createDonation($db) {
    // التحقق من البيانات المطلوبة
    if (!isset($_POST['project_id']) || !isset($_POST['amount']) || !isset($_POST['currency'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'بيانات غير مكتملة'
        ]);
        return;
    }

    try {
        // معالجة رفع ملف إثبات الدفع
        $paymentProofPath = null;
        if (isset($_FILES['payment_proof']) && $_FILES['payment_proof']['error'] === UPLOAD_ERR_OK) {
            $paymentProofPath = uploadPaymentProof($_FILES['payment_proof']);
        }

        // تحويل العملة إلى جنيه مصري
        $amount_in_egp = convertToEGP($_POST['amount'], $_POST['currency']);

        $query = "INSERT INTO donations (
            project_id,
            donor_name,
            donor_email,
            donor_phone,
            donor_country,
            amount,
            currency,
            amount_in_egp,
            message,
            is_anonymous,
            payment_proof,
            status
        ) VALUES (
            :project_id,
            :donor_name,
            :donor_email,
            :donor_phone,
            :donor_country,
            :amount,
            :currency,
            :amount_in_egp,
            :message,
            :is_anonymous,
            :payment_proof,
            :status
        )";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':project_id', $_POST['project_id']);
        $stmt->bindParam(':donor_name', $_POST['donor_name']);
        $stmt->bindParam(':donor_email', $_POST['donor_email']);
        $stmt->bindParam(':donor_phone', $_POST['donor_phone']);
        $stmt->bindParam(':donor_country', $_POST['donor_country']);
        $stmt->bindParam(':amount', $_POST['amount']);
        $stmt->bindParam(':currency', $_POST['currency']);
        $stmt->bindParam(':amount_in_egp', $amount_in_egp);
        $stmt->bindParam(':message', $_POST['message']);
        $stmt->bindParam(':is_anonymous', $_POST['is_anonymous'], PDO::PARAM_BOOL);
        $stmt->bindParam(':payment_proof', $paymentProofPath);
        $stmt->bindParam(':status', $_POST['status']);
        
        if ($stmt->execute()) {
            $donation_id = $db->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إرسال التبرع بنجاح',
                'donation_id' => $donation_id
            ]);
        } else {
            throw new Exception('فشل في حفظ التبرع');
        }
    } catch(Exception $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حفظ التبرع: ' . $exception->getMessage()
        ]);
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $exception->getMessage()
        ]);
    }
}

function updateDonation($db) {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!$data || !isset($data['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف التبرع مطلوب'
        ]);
        return;
    }
    
    try {
        $query = "UPDATE donations SET 
                  status = :status
                  WHERE id = :id";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':status', $data['status']);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث التبرع بنجاح'
            ]);
        } else {
            throw new Exception('فشل في تحديث التبرع');
        }
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في تحديث التبرع: ' . $exception->getMessage()
        ]);
    }
}

function deleteDonation($db) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف التبرع مطلوب'
        ]);
        return;
    }
    
    try {
        $query = "DELETE FROM donations WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف التبرع بنجاح'
            ]);
        } else {
            throw new Exception('فشل في حذف التبرع');
        }
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حذف التبرع: ' . $exception->getMessage()
        ]);
    }
}

// دالة تحويل العملات إلى جنيه مصري
function convertToEGP($amount, $currency) {
    $rates = [
        'EGP' => 1,      // جنيه مصري
        'USD' => 49.5,   // دولار أمريكي
        'EUR' => 53.2    // يورو
    ];
    
    return $amount * ($rates[$currency] ?? 1);
}

// دالة الحصول على إحصائيات التبرعات
function getDonationStats($db) {
    try {
        $query = "SELECT 
            COUNT(*) as total_donations,
            SUM(amount_in_egp) as total_amount_egp,
            COUNT(DISTINCT project_id) as projects_with_donations,
            AVG(amount_in_egp) as average_donation
        FROM donations 
        WHERE status = 'approved'";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    } catch(PDOException $exception) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب الإحصائيات: ' . $exception->getMessage()
        ]);
    }
}
?>
