
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowRight, Upload, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

const AddProject = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    targetAmount: '',
    raisedAmount: '',
    donorsCount: '',
    status: 'نشط',
    image_url: '',
    urgent: false,
    daysLeft: '',
    detailedDescription: ''
  });

  const [projectImage, setProjectImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const categories = [
    'طبي',
    'غذائي',
    'تعليمي',
    'إسكان',
    'مياه',
    'ملابس',
    'كهرباء',
    'أخرى'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // التحقق من نوع الملف
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يُسمح بالصور فقط (JPG, PNG, GIF, WebP)');
        return;
      }

      // التحقق من حجم الملف (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً. الحد الأقصى 10MB');
        return;
      }

      setProjectImage(file);

      // إنشاء معاينة للصورة
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // مسح رابط الصورة إذا كان موجوداً
      setFormData(prev => ({ ...prev, image_url: '' }));
    }
  };

  const removeImage = () => {
    setProjectImage(null);
    setImagePreview(null);
    setFormData(prev => ({ ...prev, image_url: '' }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.category || !formData.targetAmount || !formData.description) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (isNaN(Number(formData.targetAmount)) || Number(formData.targetAmount) <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    setLoading(true);

    try {
      // استخدام FormData لدعم رفع الصور
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.detailedDescription || formData.description);
      formDataToSend.append('category', formData.category);
      formDataToSend.append('target_amount', formData.targetAmount);
      formDataToSend.append('raised_amount', formData.raisedAmount || '0');
      formDataToSend.append('donors_count', formData.donorsCount || '0');
      formDataToSend.append('status', formData.status);

      // إضافة الصورة إذا كانت موجودة
      if (projectImage) {
        formDataToSend.append('project_image', projectImage);
      } else if (formData.image_url) {
        // إذا لم يتم رفع صورة ولكن هناك رابط صورة
        formDataToSend.append('image_url', formData.image_url);
      }

      const response = await fetch('http://localhost/gaza/api/projects.php', {
        method: 'POST',
        body: formDataToSend // لا نحتاج Content-Type header مع FormData
      });

      const data = await response.json();

      if (data.success) {
        alert('تم إنشاء المشروع بنجاح!');
        navigate('/admin/projects');
      } else {
        alert(data.message || 'خطأ في إنشاء المشروع');
      }
    } catch (error) {
      alert('خطأ في الاتصال بالخادم');
      console.error('Error creating project:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link
          to="/admin/projects"
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowRight className="h-5 w-5" />
        </Link>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 font-amiri">إضافة مشروع جديد</h2>
          <p className="text-gray-600">قم بملء جميع البيانات المطلوبة لإضافة مشروع جديد</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">المعلومات الأساسية</h3>
            
            <div>
              <Label htmlFor="title">عنوان المشروع *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="أدخل عنوان المشروع"
                required
              />
            </div>

            <div>
              <Label htmlFor="category">فئة المشروع *</Label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent"
                required
              >
                <option value="">اختر الفئة</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="targetAmount">المبلغ المستهدف (ر.س) *</Label>
              <Input
                id="targetAmount"
                name="targetAmount"
                type="number"
                value={formData.targetAmount}
                onChange={handleInputChange}
                placeholder="0"
                required
              />
            </div>

            <div>
              <Label htmlFor="raisedAmount">المبلغ المجمع حالياً (ر.س)</Label>
              <Input
                id="raisedAmount"
                name="raisedAmount"
                type="number"
                value={formData.raisedAmount}
                onChange={handleInputChange}
                placeholder="0"
              />
              <p className="text-xs text-gray-500 mt-1">
                اتركه فارغاً ليبدأ من 0، أو أدخل المبلغ المجمع حالياً
              </p>
            </div>

            <div>
              <Label htmlFor="donorsCount">عدد المتبرعين الحاليين</Label>
              <Input
                id="donorsCount"
                name="donorsCount"
                type="number"
                value={formData.donorsCount}
                onChange={handleInputChange}
                placeholder="0"
              />
              <p className="text-xs text-gray-500 mt-1">
                اتركه فارغاً ليبدأ من 0، أو أدخل عدد المتبرعين الحاليين
              </p>
            </div>

            <div>
              <Label htmlFor="status">حالة المشروع</Label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gaza-green focus:border-transparent"
              >
                <option value="نشط">نشط</option>
                <option value="مكتمل">مكتمل</option>
                <option value="متوقف">متوقف</option>
                <option value="عاجل">عاجل</option>
              </select>
            </div>

            <div>
              <Label htmlFor="daysLeft">عدد الأيام المتبقية</Label>
              <Input
                id="daysLeft"
                name="daysLeft"
                type="number"
                value={formData.daysLeft}
                onChange={handleInputChange}
                placeholder="30"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="urgent"
                name="urgent"
                checked={formData.urgent}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-gaza-green focus:ring-gaza-green"
              />
              <Label htmlFor="urgent">مشروع عاجل</Label>
            </div>
          </div>

          {/* Image Upload */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 font-amiri">صورة المشروع</h3>

            {/* رفع صورة من الجهاز */}
            <div>
              <Label htmlFor="project_image">رفع صورة من الجهاز</Label>
              <div className="mt-2">
                <input
                  id="project_image"
                  name="project_image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-gaza-green file:text-white hover:file:bg-gaza-green/90"
                />
                <p className="text-xs text-gray-500 mt-1">
                  يُسمح بالصور فقط (JPG, PNG, GIF, WebP) - حد أقصى 10MB
                </p>
              </div>
            </div>

            {/* أو رابط صورة */}
            {!projectImage && (
              <div>
                <Label htmlFor="image_url">أو رابط الصورة</Label>
                <Input
                  id="image_url"
                  name="image_url"
                  type="url"
                  value={formData.image_url}
                  onChange={handleInputChange}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            )}

            {/* معاينة الصورة المرفوعة */}
            {imagePreview && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-600">معاينة الصورة المرفوعة:</p>
                  <button
                    type="button"
                    onClick={removeImage}
                    className="text-red-600 hover:text-red-800 text-sm flex items-center gap-1"
                  >
                    <X className="h-4 w-4" />
                    إزالة
                  </button>
                </div>
                <img
                  src={imagePreview}
                  alt="معاينة الصورة"
                  className="max-w-full h-48 object-cover rounded-lg mx-auto"
                />
              </div>
            )}

            {/* معاينة الصورة من الرابط */}
            {formData.image_url && !projectImage && (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-2">معاينة الصورة من الرابط:</p>
                <img
                  src={formData.image_url}
                  alt="معاينة الصورة"
                  className="max-w-full h-48 object-cover rounded-lg mx-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 font-amiri">تفاصيل المشروع</h3>
          
          <div>
            <Label htmlFor="description">وصف مختصر *</Label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="وصف مختصر للمشروع (سيظهر في البطاقة)"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
              required
            />
          </div>

          <div>
            <Label htmlFor="detailedDescription">الوصف التفصيلي</Label>
            <textarea
              id="detailedDescription"
              name="detailedDescription"
              value={formData.detailedDescription}
              onChange={handleInputChange}
              rows={6}
              placeholder="وصف تفصيلي للمشروع وأهدافه وكيفية استخدام التبرعات"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-gaza-green focus:border-transparent resize-none"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <Link
            to="/admin/projects"
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            إلغاء
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-gaza-green text-white rounded-lg hover:bg-gaza-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </>
            ) : (
              'إضافة المشروع'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddProject;
