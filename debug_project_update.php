<?php
// تشخيص مشكلة تحديث المشروع
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-HTTP-Method-Override');

// تفعيل عرض الأخطاء للتشخيص
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo json_encode([
    'debug_info' => [
        'php_version' => phpversion(),
        'post_data' => $_POST,
        'files_data' => $_FILES,
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set',
        'server_time' => date('Y-m-d H:i:s')
    ]
], JSON_UNESCAPED_UNICODE);

try {
    require_once 'api/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    // اختبار الاتصال
    $testQuery = "SELECT COUNT(*) as total FROM projects";
    $stmt = $db->prepare($testQuery);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\n" . json_encode([
        'database_connection' => 'success',
        'total_projects' => $result['total']
    ], JSON_UNESCAPED_UNICODE);
    
    // اختبار وجود عمود donors_count
    $columnsQuery = "SHOW COLUMNS FROM projects";
    $stmt = $db->prepare($columnsQuery);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n" . json_encode([
        'table_columns' => $columns
    ], JSON_UNESCAPED_UNICODE);
    
    // اختبار تحديث مشروع (محاكاة)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
        $project_id = intval($_POST['id']);
        
        // جلب المشروع الحالي
        $selectQuery = "SELECT * FROM projects WHERE id = :id";
        $stmt = $db->prepare($selectQuery);
        $stmt->bindParam(':id', $project_id);
        $stmt->execute();
        $currentProject = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "\n" . json_encode([
            'current_project' => $currentProject
        ], JSON_UNESCAPED_UNICODE);
        
        // محاولة تحديث المشروع
        $updateQuery = "UPDATE projects SET 
                        title = :title,
                        description = :description,
                        category = :category,
                        target_amount = :target_amount,
                        raised_amount = :raised_amount,
                        donors_count = :donors_count,
                        status = :status
                        WHERE id = :id";
        
        $stmt = $db->prepare($updateQuery);
        $stmt->bindParam(':id', $project_id);
        $stmt->bindParam(':title', $_POST['title']);
        $stmt->bindParam(':description', $_POST['description']);
        $stmt->bindParam(':category', $_POST['category']);
        $stmt->bindParam(':target_amount', $_POST['target_amount']);
        $stmt->bindParam(':raised_amount', $_POST['raised_amount']);
        $stmt->bindParam(':donors_count', $_POST['donors_count']);
        $stmt->bindParam(':status', $_POST['status']);
        
        if ($stmt->execute()) {
            echo "\n" . json_encode([
                'update_result' => 'success',
                'affected_rows' => $stmt->rowCount()
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo "\n" . json_encode([
                'update_result' => 'failed',
                'error_info' => $stmt->errorInfo()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
} catch (Exception $e) {
    echo "\n" . json_encode([
        'error' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
