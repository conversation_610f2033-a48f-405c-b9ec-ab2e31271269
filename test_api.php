<?php
// اختبار سريع لـ API التبرعات
header('Content-Type: application/json; charset=utf-8');

// تضمين ملف قاعدة البيانات
require_once 'api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // اختبار الاتصال بقاعدة البيانات
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // اختبار جلب المشاريع
    $projects_query = "SELECT id, title FROM projects LIMIT 3";
    $projects_stmt = $db->prepare($projects_query);
    $projects_stmt->execute();
    $projects = $projects_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 المشاريع المتاحة:\n";
    foreach ($projects as $project) {
        echo "- {$project['id']}: {$project['title']}\n";
    }
    echo "\n";
    
    // اختبار جلب التبرعات
    $donations_query = "SELECT 
        d.id,
        d.project_id,
        d.donor_name,
        d.donor_email,
        d.donor_phone,
        d.donor_country,
        d.amount,
        d.currency,
        d.amount_in_egp,
        d.message,
        d.is_anonymous,
        d.payment_proof,
        d.status,
        d.created_at,
        p.title as project_title
    FROM donations d 
    LEFT JOIN projects p ON d.project_id = p.id
    ORDER BY d.created_at DESC 
    LIMIT 5";
    
    $donations_stmt = $db->prepare($donations_query);
    $donations_stmt->execute();
    $donations = $donations_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "💰 التبرعات الأخيرة:\n";
    if (empty($donations)) {
        echo "- لا توجد تبرعات في قاعدة البيانات\n";
    } else {
        foreach ($donations as $donation) {
            $donor_name = $donation['is_anonymous'] ? 'متبرع مجهول' : ($donation['donor_name'] ?: 'غير محدد');
            $project_title = $donation['project_title'] ?: 'مشروع غير محدد';
            echo "- {$donation['id']}: {$donor_name} تبرع بـ {$donation['amount']} {$donation['currency']} لمشروع {$project_title}\n";
        }
    }
    echo "\n";
    
    // اختبار إحصائيات التبرعات
    $stats_query = "SELECT 
        COUNT(*) as total_donations,
        SUM(amount_in_egp) as total_amount,
        AVG(amount_in_egp) as avg_amount,
        COUNT(DISTINCT project_id) as supported_projects
    FROM donations 
    WHERE status = 'approved'";
    
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "📊 إحصائيات التبرعات:\n";
    echo "- إجمالي التبرعات: {$stats['total_donations']}\n";
    echo "- إجمالي المبلغ: " . number_format($stats['total_amount'], 2) . " ج.م\n";
    echo "- متوسط التبرع: " . number_format($stats['avg_amount'], 2) . " ج.م\n";
    echo "- المشاريع المدعومة: {$stats['supported_projects']}\n\n";
    
    // اختبار تحويل العملات
    echo "💱 اختبار تحويل العملات:\n";
    $rates = ['EGP' => 1, 'USD' => 49.5, 'EUR' => 53.2];
    $test_amounts = [
        ['amount' => 100, 'currency' => 'USD'],
        ['amount' => 50, 'currency' => 'EUR'],
        ['amount' => 1000, 'currency' => 'EGP']
    ];
    
    foreach ($test_amounts as $test) {
        $converted = $test['amount'] * $rates[$test['currency']];
        echo "- {$test['amount']} {$test['currency']} = {$converted} EGP\n";
    }
    echo "\n";
    
    echo "✅ جميع الاختبارات تمت بنجاح!";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
