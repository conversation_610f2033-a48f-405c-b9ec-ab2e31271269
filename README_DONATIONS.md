# نظام التبرعات - موقع دعم غزة

## 🎯 نظرة عامة

تم تطوير نظام تبرعات متكامل يدعم العملات المتعددة مع إدارة شاملة للتبرعات من لوحة الإدارة.

## 💰 العملات المدعومة

### العملات الأساسية:
- **جنيه مصري (EGP)** - العملة الأساسية
- **دولار أمريكي (USD)** 
- **يورو (EUR)**

### معدلات التحويل:
```php
$rates = [
    'EGP' => 1,      // جنيه مصري (أساسي)
    'USD' => 49.5,   // دولار أمريكي
    'EUR' => 53.2    // يورو
];
```

## 📋 هيكل جدول التبرعات

```sql
CREATE TABLE donations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,                    -- م<PERSON>ر<PERSON> المشروع
    donor_name VARCHAR(255),                    -- اسم المتبرع
    donor_email VARCHAR(255),                   -- بريد المتبرع
    donor_phone VARCHAR(50),                    -- رقم الهاتف
    donor_country VARCHAR(100),                 -- دولة المتبرع
    amount DECIMAL(10,2) NOT NULL,              -- المبلغ بالعملة الأصلية
    currency ENUM('EGP', 'USD', 'EUR'),         -- العملة
    amount_in_egp DECIMAL(10,2) NOT NULL,       -- المبلغ بالجنيه المصري
    message TEXT,                               -- رسالة المتبرع
    is_anonymous BOOLEAN DEFAULT FALSE,         -- تبرع مجهول
    payment_proof VARCHAR(255),                 -- إثبات الدفع
    status ENUM('pending', 'approved', 'rejected'), -- حالة التبرع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 API التبرعات

### الرابط الأساسي:
```
http://localhost/gaza/api/donations.php
```

### العمليات المدعومة:

#### 1. جلب التبرعات (GET)
```javascript
// جلب جميع التبرعات
fetch('http://localhost/gaza/api/donations.php')

// جلب تبرعات مشروع محدد
fetch('http://localhost/gaza/api/donations.php?project_id=1')
```

#### 2. إضافة تبرع (POST)
```javascript
const donationData = {
    project_id: 1,
    donor_name: 'أحمد محمد',
    donor_email: '<EMAIL>',
    donor_phone: '+201234567890',
    donor_country: 'مصر',
    amount: 100.00,
    currency: 'USD',
    message: 'بارك الله فيكم',
    is_anonymous: false,
    payment_proof: 'receipt.jpg',
    status: 'pending'
};

fetch('http://localhost/gaza/api/donations.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(donationData)
});
```

#### 3. تحديث حالة التبرع (PUT)
```javascript
const updateData = {
    id: 1,
    status: 'approved'
};

fetch('http://localhost/gaza/api/donations.php', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updateData)
});
```

#### 4. حذف تبرع (DELETE)
```javascript
fetch('http://localhost/gaza/api/donations.php?id=1', {
    method: 'DELETE'
});
```

## 🎨 واجهة المستخدم

### نموذج التبرع:
- **معلومات شخصية**: الاسم، البلد، الواتساب، البريد الإلكتروني
- **قيمة التبرع**: المبلغ والعملة مع مبالغ سريعة
- **خيارات إضافية**: رسالة، تبرع مجهول
- **إثبات الدفع**: رفع ملف أو صورة

### المبالغ السريعة حسب العملة:

#### جنيه مصري (EGP):
- 100، 250، 500، 1000، 2500، 5000 ج.م

#### دولار أمريكي (USD):
- $5، $10، $25، $50، $100، $250

#### يورو (EUR):
- €5، €10، €25، €50، €100، €200

## 🛠️ لوحة الإدارة

### صفحة التبرعات (`/admin/donations`):

#### الإحصائيات:
- إجمالي التبرعات
- المبلغ الإجمالي (بالجنيه المصري)
- المشاريع المدعومة
- متوسط التبرع

#### أدوات الإدارة:
- **البحث**: بالاسم، المشروع، أو البريد الإلكتروني
- **الفلترة**: حسب الحالة (في الانتظار، مقبول، مرفوض)
- **الإجراءات**: عرض التفاصيل، قبول، رفض

#### جدول التبرعات:
- معلومات المتبرع
- المشروع المدعوم
- المبلغ بالعملة الأصلية والجنيه المصري
- الحالة والتاريخ
- أزرار الإجراءات

## 🔧 التحويل التلقائي للعملات

```php
function convertToEGP($amount, $currency) {
    $rates = [
        'EGP' => 1,      // جنيه مصري
        'USD' => 49.5,   // دولار أمريكي
        'EUR' => 53.2    // يورو
    ];
    
    return $amount * ($rates[$currency] ?? 1);
}
```

## 📊 حالات التبرع

### pending (في الانتظار):
- التبرع الجديد يحتاج مراجعة
- يظهر في لوحة الإدارة للمراجعة
- لا يؤثر على المبلغ المجمع للمشروع

### approved (مقبول):
- تم قبول التبرع من الإدارة
- يضاف للمبلغ المجمع للمشروع
- يظهر في الإحصائيات

### rejected (مرفوض):
- تم رفض التبرع
- لا يؤثر على المبلغ المجمع
- يحفظ للسجلات

## 🔄 التحديث التلقائي

### Triggers قاعدة البيانات:
- تحديث `raised_amount` عند إضافة تبرع مقبول
- تحديث `raised_amount` عند حذف تبرع
- تغيير حالة المشروع إلى "مكتمل" عند الوصول للهدف

## 🎯 مثال كامل للاستخدام

### 1. المتبرع يملأ النموذج:
```javascript
const formData = {
    fullName: 'أحمد محمد',
    country: 'مصر',
    whatsapp: '+201234567890',
    email: '<EMAIL>',
    amount: '100',
    currency: 'USD',
    message: 'بارك الله فيكم',
    isAnonymous: false
};
```

### 2. النظام يحول العملة:
```
100 USD × 49.5 = 4,950 EGP
```

### 3. يحفظ في قاعدة البيانات:
```sql
INSERT INTO donations (
    project_id, donor_name, amount, currency, amount_in_egp, status
) VALUES (
    1, 'أحمد محمد', 100.00, 'USD', 4950.00, 'pending'
);
```

### 4. الإدارة تراجع وتقبل:
```sql
UPDATE donations SET status = 'approved' WHERE id = 1;
```

### 5. يتم تحديث المشروع تلقائياً:
```sql
UPDATE projects SET raised_amount = raised_amount + 4950.00 WHERE id = 1;
```

## 🚀 التشغيل والاختبار

### 1. تأكد من إعداد قاعدة البيانات:
```bash
# استيراد ملف SQL المحدث
mysql -u root gaza_donations < database/gaza_donations.sql
```

### 2. اختبار API:
```bash
# اختبار جلب التبرعات
curl http://localhost/gaza/api/donations.php

# اختبار إضافة تبرع
curl -X POST http://localhost/gaza/api/donations.php \
  -H "Content-Type: application/json" \
  -d '{"project_id":1,"donor_name":"Test","amount":100,"currency":"USD"}'
```

### 3. اختبار الواجهة:
- افتح الموقع: `http://localhost:5173`
- انقر على "تبرع الآن" في أي مشروع
- املأ النموذج واختبر العملات المختلفة
- تحقق من لوحة الإدارة: `http://localhost:5173/admin/donations`

## 🔍 استكشاف الأخطاء

### مشكلة: التبرع لا يظهر في لوحة الإدارة
**الحل:**
1. تحقق من console المتصفح للأخطاء
2. تأكد من تشغيل Apache و MySQL
3. تحقق من رابط API: `http://localhost/gaza/api/donations.php`

### مشكلة: خطأ في تحويل العملة
**الحل:**
1. تحقق من دالة `convertToEGP` في `api/donations.php`
2. تأكد من إرسال العملة بشكل صحيح
3. تحقق من قيم `amount` و `amount_in_egp` في قاعدة البيانات

### مشكلة: لا يتم تحديث المبلغ المجمع
**الحل:**
1. تحقق من وجود Triggers في قاعدة البيانات
2. تأكد من أن حالة التبرع `approved`
3. راجع جدول `projects` للتأكد من التحديث
