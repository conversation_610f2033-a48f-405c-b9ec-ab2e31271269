<?php
// إصلاح هيكل قاعدة البيانات - إضافة العمود المفقود
header('Content-Type: application/json; charset=utf-8');

try {
    require_once 'api/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    $results = [];
    
    // التحقق من وجود عمود donors_count
    $checkQuery = "SELECT COUNT(*) as col_exists 
                   FROM information_schema.columns 
                   WHERE table_schema = 'gaza_donations' 
                   AND table_name = 'projects' 
                   AND column_name = 'donors_count'";
    
    $stmt = $db->prepare($checkQuery);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['col_exists'] == 0) {
        // إضافة العمود
        $addColumnQuery = "ALTER TABLE projects ADD COLUMN donors_count INT NOT NULL DEFAULT 0 AFTER raised_amount";
        $db->exec($addColumnQuery);
        $results[] = "تم إضافة عمود donors_count بنجاح";
        
        // تحديث قيم donors_count بناءً على التبرعات الموجودة
        $updateQuery = "UPDATE projects p 
                        SET donors_count = (
                            SELECT COUNT(*) 
                            FROM donations d 
                            WHERE d.project_id = p.id
                        )";
        $db->exec($updateQuery);
        $results[] = "تم تحديث قيم donors_count بناءً على التبرعات الموجودة";
    } else {
        $results[] = "عمود donors_count موجود بالفعل";
    }
    
    // التحقق من هيكل الجدول الحالي
    $structureQuery = "DESCRIBE projects";
    $stmt = $db->prepare($structureQuery);
    $stmt->execute();
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // اختبار استعلام جلب المشاريع
    $testQuery = "SELECT id, title, target_amount, raised_amount, donors_count, status FROM projects LIMIT 3";
    $stmt = $db->prepare($testQuery);
    $stmt->execute();
    $testData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إصلاح هيكل قاعدة البيانات بنجاح',
        'results' => $results,
        'table_structure' => $structure,
        'test_data' => $testData
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في إصلاح قاعدة البيانات: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
