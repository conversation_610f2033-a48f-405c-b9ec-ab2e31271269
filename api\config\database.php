<?php
// ملف قاعدة البيانات - لا يحتوي على headers لتجنب التضارب

class Database {
    private $host = 'localhost';
    private $db_name = 'gaza_donations';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            // تسجيل الخطأ بدلاً من عرضه مباشرة
            error_log('Database connection error: ' . $exception->getMessage());
            throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $exception->getMessage());
        }
        
        return $this->conn;
    }
    
    public function createTables() {
        try {
            // إنشاء جدول المشاريع
            $query = "CREATE TABLE IF NOT EXISTS projects (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                category VARCHAR(100) NOT NULL,
                target_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
                raised_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
                donors_count INT NOT NULL DEFAULT 0,
                status ENUM('نشط', 'متوقف', 'مكتمل') DEFAULT 'نشط',
                image_url VARCHAR(500),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($query);
            
            // إنشاء جدول التبرعات
            $query2 = "CREATE TABLE IF NOT EXISTS donations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                project_id INT NOT NULL,
                donor_name VARCHAR(255),
                donor_email VARCHAR(255),
                donor_phone VARCHAR(50),
                donor_country VARCHAR(100),
                amount DECIMAL(10,2) NOT NULL,
                currency ENUM('EGP', 'USD', 'EUR') DEFAULT 'EGP',
                amount_in_egp DECIMAL(10,2) NOT NULL,
                message TEXT,
                is_anonymous BOOLEAN DEFAULT FALSE,
                payment_proof VARCHAR(255),
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($query2);
            
            return true;
        } catch(PDOException $exception) {
            return false;
        }
    }
}
?>
