<?php
// اختبار قاعدة البيانات لنظام رفع الملفات
header('Content-Type: application/json; charset=utf-8');

require_once 'api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // فحص جدول donations
    $query = "DESCRIBE donations";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 هيكل جدول donations:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} ";
        if ($column['Null'] === 'NO') echo "(مطلوب)";
        if ($column['Default'] !== null) echo " [افتراضي: {$column['Default']}]";
        echo "\n";
    }
    echo "\n";
    
    // التحقق من وجود عمود payment_proof
    $hasPaymentProof = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'payment_proof') {
            $hasPaymentProof = true;
            break;
        }
    }
    
    if ($hasPaymentProof) {
        echo "✅ عمود payment_proof موجود في الجدول\n";
    } else {
        echo "❌ عمود payment_proof غير موجود في الجدول\n";
        echo "يجب تشغيل الأمر التالي:\n";
        echo "ALTER TABLE donations ADD COLUMN payment_proof VARCHAR(255) NULL;\n";
    }
    echo "\n";
    
    // فحص البيانات الموجودة
    $query = "SELECT COUNT(*) as total FROM donations";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "📊 إجمالي التبرعات: {$result['total']}\n";
    
    // فحص التبرعات التي تحتوي على إثبات دفع
    $query = "SELECT COUNT(*) as with_proof FROM donations WHERE payment_proof IS NOT NULL AND payment_proof != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "📎 التبرعات مع إثبات دفع: {$result['with_proof']}\n\n";
    
    // عرض آخر 3 تبرعات
    $query = "SELECT id, donor_name, amount, currency, payment_proof, created_at FROM donations ORDER BY created_at DESC LIMIT 3";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📝 آخر التبرعات:\n";
    foreach ($donations as $donation) {
        $proof = $donation['payment_proof'] ? '✅ يوجد' : '❌ لا يوجد';
        echo "- {$donation['id']}: {$donation['donor_name']} - {$donation['amount']} {$donation['currency']} - إثبات: {$proof}\n";
    }
    echo "\n";
    
    // فحص مجلد الرفع
    $uploadDir = 'uploads/payment_proofs/';
    if (is_dir($uploadDir)) {
        echo "✅ مجلد الرفع موجود: {$uploadDir}\n";
        
        if (is_writable($uploadDir)) {
            echo "✅ مجلد الرفع قابل للكتابة\n";
        } else {
            echo "❌ مجلد الرفع غير قابل للكتابة\n";
        }
        
        $files = scandir($uploadDir);
        $files = array_filter($files, function($file) {
            return !in_array($file, ['.', '..', 'index.php']);
        });
        
        echo "📁 عدد الملفات المرفوعة: " . count($files) . "\n";
        
        if (!empty($files)) {
            echo "📋 الملفات:\n";
            foreach (array_slice($files, 0, 5) as $file) {
                $filePath = $uploadDir . $file;
                $fileSize = filesize($filePath);
                echo "- {$file} (" . number_format($fileSize / 1024, 2) . " KB)\n";
            }
            if (count($files) > 5) {
                echo "... و " . (count($files) - 5) . " ملف آخر\n";
            }
        }
    } else {
        echo "❌ مجلد الرفع غير موجود: {$uploadDir}\n";
    }
    echo "\n";
    
    // فحص إعدادات PHP
    echo "⚙️ إعدادات PHP للرفع:\n";
    echo "- file_uploads: " . (ini_get('file_uploads') ? 'مفعل' : 'معطل') . "\n";
    echo "- upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
    echo "- post_max_size: " . ini_get('post_max_size') . "\n";
    echo "- max_file_uploads: " . ini_get('max_file_uploads') . "\n";
    echo "\n";
    
    echo "🎉 جميع الفحوصات اكتملت!";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
